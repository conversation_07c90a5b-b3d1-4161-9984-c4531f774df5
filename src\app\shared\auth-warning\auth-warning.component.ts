import { Component, inject, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthService } from '../../core/auth/auth.service';

@Component({
  selector: 'app-auth-warning',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './auth-warning.component.html',
  styleUrls: ['./auth-warning.component.scss']
})
export class AuthWarningComponent {
  // Inject auth service
  private authService = inject(AuthService);

  // Auth signals
  isAuthenticated = computed(() => this.authService.isAuthenticated());
  currentUser = computed(() => this.authService.currentUser());
}
