import { Injectable, inject } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { SavingPlan, SavingPeriod } from '../models/financial-savings.model';
import { AppStorageService } from './app-storage.service';
import { FinancialSavingsDataService } from '../core/financial-savings/financial-savings-data.service';

@Injectable({
  providedIn: 'root'
})
export class FinancialSavingsService {
  private savingPlans: SavingPlan[] = [];
  private savingPlansSubject = new BehaviorSubject<SavingPlan[]>([]);
  // Saving plans will be managed by AppStorageService

  // Inject the new signal-based data service
  private financialSavingsDataService = inject(FinancialSavingsDataService);

  constructor(private appStorage: AppStorageService) {
    // Load saving plans from storage or initialize with empty array
    this.loadSavingPlans();
  }

  private loadSavingPlans(): void {
    // Check if we have a method for saving plans in AppStorageService
    // If not, we'll use our own implementation with localStorage
    const storedPlans = this.getSavedPlans();
    
    if (storedPlans && storedPlans.length > 0) {
      this.savingPlans = storedPlans;
    } else {
      // Initialize with empty array
      this.savingPlans = [];
    }

    // Update the subject with the loaded data
    this.savingPlansSubject.next([...this.savingPlans]);
  }

  private getSavedPlans(): SavingPlan[] {
    const plans = this.appStorage.getSavingPlans();
    return plans.map(plan => ({
      ...plan,
      startDate: new Date(plan.startDate),
      endDate: plan.endDate ? new Date(plan.endDate) : undefined,
      lastUpdate: new Date(plan.lastUpdate)
    }));
  }

  private savePlansToStorage(): void {
    this.appStorage.saveSavingPlans(this.savingPlans);
  }

  getSavingPlans(): Observable<SavingPlan[]> {
    // Use the signal-based service for new functionality, but maintain backward compatibility
    const signalPlans = this.financialSavingsDataService.plans();
    if (signalPlans.length > 0) {
      const legacyPlans = signalPlans.map(plan => ({
        id: plan.id,
        name: plan.name,
        objective: plan.objective,
        current: plan.current,
        period: plan.period,
        frequency: plan.frequency,
        startDate: plan.startDate,
        endDate: plan.endDate,
        interestRate: plan.interestRate,
        lastUpdate: plan.lastUpdate,
        percentComplete: plan.percentComplete,
        durationAmount: plan.durationAmount,
        durationValue: plan.durationValue,
        durationType: plan.durationType
      }));
      return of(legacyPlans);
    }
    return this.savingPlansSubject.asObservable();
  }

  getSavingPlanById(id: string): SavingPlan | undefined {
    // Try signal-based service first
    const signalPlan = this.financialSavingsDataService.getSavingPlanById(id);
    if (signalPlan) {
      return {
        id: signalPlan.id,
        name: signalPlan.name,
        objective: signalPlan.objective,
        current: signalPlan.current,
        period: signalPlan.period,
        frequency: signalPlan.frequency,
        startDate: signalPlan.startDate,
        endDate: signalPlan.endDate,
        interestRate: signalPlan.interestRate,
        lastUpdate: signalPlan.lastUpdate,
        percentComplete: signalPlan.percentComplete,
        durationAmount: signalPlan.durationAmount,
        durationValue: signalPlan.durationValue,
        durationType: signalPlan.durationType
      };
    }

    // Fallback to legacy storage
    return this.savingPlans.find(plan => plan.id === id);
  }

  addSavingPlan(plan: SavingPlan): void {
    // Use the signal-based service for new plans
    this.financialSavingsDataService.addSavingPlan({
      name: plan.name,
      objective: plan.objective,
      current: plan.current,
      period: plan.period,
      frequency: plan.frequency,
      startDate: plan.startDate || new Date(),
      endDate: plan.endDate,
      interestRate: plan.interestRate,
      durationAmount: plan.durationAmount,
      durationValue: plan.durationValue,
      durationType: plan.durationType
    }).subscribe(newPlan => {
      if (newPlan) {
        // Convert to legacy format for backward compatibility
        const legacyPlan: SavingPlan = {
          id: newPlan.id,
          name: newPlan.name,
          objective: newPlan.objective,
          current: newPlan.current,
          period: newPlan.period,
          frequency: newPlan.frequency,
          startDate: newPlan.startDate,
          endDate: newPlan.endDate,
          interestRate: newPlan.interestRate,
          lastUpdate: newPlan.lastUpdate,
          percentComplete: newPlan.percentComplete,
          durationAmount: newPlan.durationAmount,
          durationValue: newPlan.durationValue,
          durationType: newPlan.durationType
        };

        // Update legacy storage for components that still use it
        this.savingPlans.push(legacyPlan);
        this.savePlansToStorage();
        this.savingPlansSubject.next([...this.savingPlans]);
      }
    });
  }

  updateSavingPlan(updatedPlan: SavingPlan): void {
    // Use the signal-based service
    const signalPlan = {
      id: updatedPlan.id,
      name: updatedPlan.name,
      objective: updatedPlan.objective,
      current: updatedPlan.current,
      period: updatedPlan.period,
      frequency: updatedPlan.frequency,
      startDate: updatedPlan.startDate,
      endDate: updatedPlan.endDate,
      interestRate: updatedPlan.interestRate,
      lastUpdate: updatedPlan.lastUpdate,
      percentComplete: updatedPlan.percentComplete,
      durationAmount: updatedPlan.durationAmount,
      durationValue: updatedPlan.durationValue,
      durationType: updatedPlan.durationType,
      userId: '' // Will be set by the service
    };

    this.financialSavingsDataService.updateSavingPlan(signalPlan).subscribe(updated => {
      if (updated) {
        // Update legacy storage
        const index = this.savingPlans.findIndex(plan => plan.id === updatedPlan.id);
        if (index !== -1) {
          this.savingPlans[index] = updatedPlan;
          this.savePlansToStorage();
          this.savingPlansSubject.next([...this.savingPlans]);
        }
      }
    });
  }

  deleteSavingPlan(id: string): void {
    // Use the signal-based service
    this.financialSavingsDataService.deleteSavingPlan(id).subscribe(success => {
      if (success) {
        // Update legacy storage
        this.savingPlans = this.savingPlans.filter(plan => plan.id !== id);
        this.savePlansToStorage();
        this.savingPlansSubject.next([...this.savingPlans]);
      }
    });
  }

  // Add funds to a saving plan
  addFundsToSavingPlan(id: string, amount: number): SavingPlan | undefined {
    // Use the signal-based service
    this.financialSavingsDataService.addFundsToSavingPlan(id, amount).subscribe(updatedPlan => {
      if (updatedPlan) {
        // Update legacy storage
        const index = this.savingPlans.findIndex(plan => plan.id === id);
        if (index !== -1) {
          const legacyPlan: SavingPlan = {
            id: updatedPlan.id,
            name: updatedPlan.name,
            objective: updatedPlan.objective,
            current: updatedPlan.current,
            period: updatedPlan.period,
            frequency: updatedPlan.frequency,
            startDate: updatedPlan.startDate,
            endDate: updatedPlan.endDate,
            interestRate: updatedPlan.interestRate,
            lastUpdate: updatedPlan.lastUpdate,
            percentComplete: updatedPlan.percentComplete,
            durationAmount: updatedPlan.durationAmount,
            durationValue: updatedPlan.durationValue,
            durationType: updatedPlan.durationType
          };

          this.savingPlans[index] = legacyPlan;
          this.savePlansToStorage();
          this.savingPlansSubject.next([...this.savingPlans]);
        }
      }
    });

    // Return the current plan for immediate use
    return this.getSavingPlanById(id);
  }
}
