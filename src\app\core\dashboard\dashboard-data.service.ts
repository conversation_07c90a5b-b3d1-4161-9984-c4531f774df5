import { Injectable, signal, computed, effect, inject } from '@angular/core';
import { Observable, of } from 'rxjs';
import { 
  DashboardData, 
  DashboardState, 
  DashboardWidget, 
  DashboardWidgetType,
  DashboardAnalytics,
  DEFAULT_DASHBOARD_CONFIG,
  DEFAULT_DASHBOARD_WIDGETS,
  isDashboardData,
  isDashboardWidget
} from '../../interfaces/dashboard';
import { AuthService } from '../auth/auth.service';
import { BudgetDataService } from '../budget/budget-data.service';
import { FinancialSavingsDataService } from '../financial-savings/financial-savings-data.service';
import { LoyaltyCardDataService } from '../loyalty-cards/loyalty-card-data.service';

@Injectable({
  providedIn: 'root'
})
export class DashboardDataService {
  private authService = inject(AuthService);
  private budgetDataService = inject(BudgetDataService);
  private financialSavingsDataService = inject(FinancialSavingsDataService);
  private loyaltyCardDataService = inject(LoyaltyCardDataService);

  // Private signals for dashboard data
  private _dashboardState = signal<DashboardState>({
    dashboard: null,
    widgets: [],
    isLoading: false,
    lastSync: null,
    hasUnsavedChanges: false,
    error: null
  });

  private _currentUserEmail = signal<string | null>(null);

  // Public readonly signals
  readonly dashboardState = this._dashboardState.asReadonly();
  readonly dashboard = computed(() => this._dashboardState().dashboard);
  readonly widgets = computed(() => this._dashboardState().widgets);
  readonly isLoading = computed(() => this._dashboardState().isLoading);
  readonly hasUnsavedChanges = computed(() => this._dashboardState().hasUnsavedChanges);
  readonly error = computed(() => this._dashboardState().error);
  readonly currentUserEmail = this._currentUserEmail.asReadonly();

  // Computed analytics signals
  readonly analytics = computed((): DashboardAnalytics => {
    const budgetState = this.budgetDataService.budgetState();
    const savingsState = this.financialSavingsDataService.savingPlanState();
    const loyaltyCardState = this.loyaltyCardDataService.loyaltyCardState();

    return {
      totalBudget: budgetState.totalBudget,
      totalSpent: budgetState.totalSpent,
      totalSavings: savingsState.totalCurrent,
      loyaltyCardsCount: loyaltyCardState.totalCards,
      monthlyTrend: this.calculateMonthlyTrend(),
      categoryBreakdown: this.calculateCategoryBreakdown(),
      savingsProgress: this.calculateSavingsProgress()
    };
  });

  constructor() {
    // Watch for auth user changes and load corresponding dashboard data
    effect(() => {
      const authUser = this.authService.currentUser();
      console.log('DashboardDataService effect triggered - authUser:', authUser);
      if (authUser) {
        this._currentUserEmail.set(authUser.email);
        this.loadUserDashboardData(authUser.email);
      } else {
        this._currentUserEmail.set(null);
        this.clearDashboardData();
      }
    }, { allowSignalWrites: true });

    // Effect to save dashboard data when it changes
    effect(() => {
      const dashboardState = this._dashboardState();
      const userEmail = this._currentUserEmail();
      const isAuthenticated = this.authService.isAuthenticated();

      // Only save if user is authenticated and has unsaved changes
      if (isAuthenticated && userEmail && dashboardState.dashboard && dashboardState.hasUnsavedChanges) {
        this.saveDashboardDataToStorage(dashboardState, userEmail);
      }
    }, { allowSignalWrites: true });
  }

  // Load user-specific dashboard data from localStorage
  private loadUserDashboardData(userEmail: string): void {
    this._dashboardState.update(state => ({ ...state, isLoading: true, error: null }));

    try {
      const storageKey = `dashboard_${userEmail}`;
      const storedData = localStorage.getItem(storageKey);
      
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        
        // Validate and restore dashboard data
        if (isDashboardData(parsedData.dashboard)) {
          // Convert date strings back to Date objects
          parsedData.dashboard.lastUpdated = new Date(parsedData.dashboard.lastUpdated);
          
          this._dashboardState.update(state => ({
            ...state,
            dashboard: parsedData.dashboard,
            widgets: parsedData.widgets || [],
            lastSync: parsedData.lastSync ? new Date(parsedData.lastSync) : null,
            isLoading: false,
            hasUnsavedChanges: false
          }));
          
          console.log('Dashboard data loaded for user:', userEmail);
        } else {
          // Invalid data, create default dashboard
          this.createDefaultDashboard(userEmail);
        }
      } else {
        // No existing data, create default dashboard
        this.createDefaultDashboard(userEmail);
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      this._dashboardState.update(state => ({
        ...state,
        error: 'Failed to load dashboard data',
        isLoading: false
      }));
      this.createDefaultDashboard(userEmail);
    }
  }

  // Create default dashboard for new users
  private createDefaultDashboard(userEmail: string): void {
    const defaultDashboard: DashboardData = {
      id: `dashboard_${Date.now()}`,
      userId: userEmail,
      lastUpdated: new Date(),
      preferences: DEFAULT_DASHBOARD_CONFIG.preferences!,
      widgets: [],
      layout: DEFAULT_DASHBOARD_CONFIG.layout!
    };

    // Create default widgets with unique IDs
    const defaultWidgets: DashboardWidget[] = DEFAULT_DASHBOARD_WIDGETS.map((widget, index) => ({
      ...widget,
      id: `widget_${Date.now()}_${index}`
    }));

    this._dashboardState.update(state => ({
      ...state,
      dashboard: defaultDashboard,
      widgets: defaultWidgets,
      isLoading: false,
      hasUnsavedChanges: true,
      error: null
    }));

    console.log('Default dashboard created for user:', userEmail);
  }

  // Save dashboard data to localStorage
  private saveDashboardDataToStorage(dashboardState: DashboardState, userEmail: string): void {
    try {
      const storageKey = `dashboard_${userEmail}`;
      const dataToStore = {
        dashboard: dashboardState.dashboard,
        widgets: dashboardState.widgets,
        lastSync: new Date()
      };
      
      localStorage.setItem(storageKey, JSON.stringify(dataToStore));
      
      this._dashboardState.update(state => ({
        ...state,
        hasUnsavedChanges: false,
        lastSync: new Date()
      }));
      
      console.log('Dashboard data saved for user:', userEmail);
    } catch (error) {
      console.error('Error saving dashboard data:', error);
      this._dashboardState.update(state => ({
        ...state,
        error: 'Failed to save dashboard data'
      }));
    }
  }

  // Clear dashboard data (on logout)
  private clearDashboardData(): void {
    this._dashboardState.set({
      dashboard: null,
      widgets: [],
      isLoading: false,
      lastSync: null,
      hasUnsavedChanges: false,
      error: null
    });
    console.log('Dashboard data cleared');
  }

  // Public methods for dashboard management
  updateDashboardPreferences(preferences: Partial<DashboardData['preferences']>): Observable<boolean> {
    return new Observable<boolean>(observer => {
      try {
        const currentDashboard = this._dashboardState().dashboard;
        if (!currentDashboard) {
          observer.error(new Error('No dashboard found'));
          return;
        }

        const updatedDashboard: DashboardData = {
          ...currentDashboard,
          preferences: { ...currentDashboard.preferences, ...preferences },
          lastUpdated: new Date()
        };

        this._dashboardState.update(state => ({
          ...state,
          dashboard: updatedDashboard,
          hasUnsavedChanges: true
        }));

        observer.next(true);
        observer.complete();
      } catch (error) {
        observer.error(error);
      }
    });
  }

  addWidget(widget: Omit<DashboardWidget, 'id'>): Observable<DashboardWidget> {
    return new Observable<DashboardWidget>(observer => {
      try {
        const newWidget: DashboardWidget = {
          ...widget,
          id: `widget_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };

        this._dashboardState.update(state => ({
          ...state,
          widgets: [...state.widgets, newWidget],
          hasUnsavedChanges: true
        }));

        observer.next(newWidget);
        observer.complete();
      } catch (error) {
        observer.error(error);
      }
    });
  }

  updateWidget(widgetId: string, updates: Partial<DashboardWidget>): Observable<DashboardWidget | null> {
    return new Observable<DashboardWidget | null>(observer => {
      try {
        const currentWidgets = this._dashboardState().widgets;
        const widgetIndex = currentWidgets.findIndex(w => w.id === widgetId);
        
        if (widgetIndex === -1) {
          observer.next(null);
          observer.complete();
          return;
        }

        const updatedWidget = { ...currentWidgets[widgetIndex], ...updates };
        const updatedWidgets = [...currentWidgets];
        updatedWidgets[widgetIndex] = updatedWidget;

        this._dashboardState.update(state => ({
          ...state,
          widgets: updatedWidgets,
          hasUnsavedChanges: true
        }));

        observer.next(updatedWidget);
        observer.complete();
      } catch (error) {
        observer.error(error);
      }
    });
  }

  removeWidget(widgetId: string): Observable<boolean> {
    return new Observable<boolean>(observer => {
      try {
        const currentWidgets = this._dashboardState().widgets;
        const filteredWidgets = currentWidgets.filter(w => w.id !== widgetId);

        this._dashboardState.update(state => ({
          ...state,
          widgets: filteredWidgets,
          hasUnsavedChanges: true
        }));

        observer.next(true);
        observer.complete();
      } catch (error) {
        observer.error(error);
      }
    });
  }

  // Helper methods for analytics calculations
  private calculateMonthlyTrend() {
    // This would typically fetch data from multiple sources
    // For now, return empty array - to be implemented based on actual data
    return [];
  }

  private calculateCategoryBreakdown() {
    // This would calculate category breakdown from budget data
    // For now, return empty array - to be implemented based on actual data
    return [];
  }

  private calculateSavingsProgress() {
    // This would calculate savings progress from financial savings data
    // For now, return empty array - to be implemented based on actual data
    return [];
  }
}
