import { Injectable } from '@angular/core';

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  type: 'navigation' | 'paint' | 'custom';
}

@Injectable({
  providedIn: 'root'
})
export class PerformanceMonitorService {
  private metrics: PerformanceMetric[] = [];
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializePerformanceObservers();
  }

  /**
   * Initialize performance observers
   */
  private initializePerformanceObservers(): void {
    if ('PerformanceObserver' in window) {
      // Observe navigation timing
      try {
        const navObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach(entry => {
            if (entry.entryType === 'navigation') {
              const navEntry = entry as PerformanceNavigationTiming;
              this.recordMetric('DOM Content Loaded', navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart, 'navigation');
              this.recordMetric('Load Complete', navEntry.loadEventEnd - navEntry.loadEventStart, 'navigation');
              this.recordMetric('First Byte', navEntry.responseStart - navEntry.requestStart, 'navigation');
            }
          });
        });
        navObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(navObserver);
      } catch (e) {
        console.warn('Navigation timing observer not supported');
      }

      // Observe paint timing
      try {
        const paintObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach(entry => {
            if (entry.entryType === 'paint') {
              this.recordMetric(entry.name, entry.startTime, 'paint');
            }
          });
        });
        paintObserver.observe({ entryTypes: ['paint'] });
        this.observers.push(paintObserver);
      } catch (e) {
        console.warn('Paint timing observer not supported');
      }

      // Observe largest contentful paint
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          this.recordMetric('Largest Contentful Paint', lastEntry.startTime, 'paint');
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);
      } catch (e) {
        console.warn('LCP observer not supported');
      }

      // Observe first input delay
      try {
        const fidObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach(entry => {
            const fidEntry = entry as any;
            this.recordMetric('First Input Delay', fidEntry.processingStart - fidEntry.startTime, 'custom');
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.push(fidObserver);
      } catch (e) {
        console.warn('FID observer not supported');
      }
    }
  }

  /**
   * Record a custom performance metric
   */
  recordMetric(name: string, value: number, type: 'navigation' | 'paint' | 'custom' = 'custom'): void {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      type
    };
    
    this.metrics.push(metric);
    
    // Keep only last 100 metrics to prevent memory leaks
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }

    // Log important metrics
    if (this.isImportantMetric(name)) {
      console.log(`Performance: ${name} = ${value.toFixed(2)}ms`);
    }
  }

  /**
   * Check if metric is important for logging
   */
  private isImportantMetric(name: string): boolean {
    const importantMetrics = [
      'first-paint',
      'first-contentful-paint',
      'Largest Contentful Paint',
      'First Input Delay',
      'DOM Content Loaded',
      'Load Complete'
    ];
    return importantMetrics.includes(name);
  }

  /**
   * Measure component render time
   */
  measureComponentRender<T>(componentName: string, fn: () => T): T {
    const startTime = performance.now();
    const result = fn();
    const endTime = performance.now();
    
    this.recordMetric(`${componentName} Render`, endTime - startTime);
    return result;
  }

  /**
   * Measure async operation
   */
  async measureAsync<T>(operationName: string, fn: () => Promise<T>): Promise<T> {
    const startTime = performance.now();
    try {
      const result = await fn();
      const endTime = performance.now();
      this.recordMetric(operationName, endTime - startTime);
      return result;
    } catch (error) {
      const endTime = performance.now();
      this.recordMetric(`${operationName} (Error)`, endTime - startTime);
      throw error;
    }
  }

  /**
   * Get all recorded metrics
   */
  getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  /**
   * Get metrics by type
   */
  getMetricsByType(type: 'navigation' | 'paint' | 'custom'): PerformanceMetric[] {
    return this.metrics.filter(metric => metric.type === type);
  }

  /**
   * Get average metric value by name
   */
  getAverageMetric(name: string): number {
    const matchingMetrics = this.metrics.filter(metric => metric.name === name);
    if (matchingMetrics.length === 0) return 0;
    
    const sum = matchingMetrics.reduce((acc, metric) => acc + metric.value, 0);
    return sum / matchingMetrics.length;
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): any {
    return {
      totalMetrics: this.metrics.length,
      averageRenderTime: this.getAverageMetric('Component Render'),
      paintMetrics: this.getMetricsByType('paint'),
      navigationMetrics: this.getMetricsByType('navigation'),
      customMetrics: this.getMetricsByType('custom')
    };
  }

  /**
   * Cleanup observers
   */
  destroy(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.clearMetrics();
  }
}
