import { Injectable, inject, effect } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { AuthService } from '../core/auth/auth.service';

export interface CategoryExpense {
  category: string;
  amount: number;
  color: string;
}

@Injectable({
  providedIn: 'root'
})
export class ExpenseTrackerService {
  private readonly STORAGE_KEY_PREFIX = 'recetto-category-expenses';
  private authService = inject(AuthService);

  // Default category colors matching the dashboard
  private categoryColors: { [key: string]: string } = {
    'food': '#A78BFA',
    'clothes': '#F9A8D4',
    'leisure': '#60A5FA',
    'medicines': '#FBBF24',
    'healthy': '#FBBF24' // Map healthy to medicines color
  };

  // BehaviorSubject to track category expenses
  private categoryExpensesSubject = new BehaviorSubject<CategoryExpense[]>(this.loadExpensesFromStorage());
  public categoryExpenses$ = this.categoryExpensesSubject.asObservable();

  constructor() {
    // Watch for user changes and reload expenses using effect
    effect(() => {
      const user = this.authService.currentUser();
      console.log('ExpenseTracker: User effect triggered, user:', user);
      if (user?.email) {
        console.log('ExpenseTracker: User changed, reloading expenses for:', user.email);
        const userExpenses = this.loadExpensesFromStorage();
        this.categoryExpensesSubject.next(userExpenses);
      } else {
        console.log('ExpenseTracker: User logged out, resetting to default expenses');
        const defaultExpenses = this.getDefaultExpenses();
        this.categoryExpensesSubject.next(defaultExpenses);
      }
    }, { allowSignalWrites: true });
  }

  /**
   * Get user-specific storage key
   */
  private getUserStorageKey(): string | null {
    const currentUser = this.authService.currentUser();
    if (!currentUser?.email) {
      return null;
    }
    return `${this.STORAGE_KEY_PREFIX}_${currentUser.email}`;
  }

  /**
   * Get default expenses for all categories
   */
  private getDefaultExpenses(): CategoryExpense[] {
    return [
      { category: 'food', amount: 0, color: this.categoryColors['food'] },
      { category: 'clothes', amount: 0, color: this.categoryColors['clothes'] },
      { category: 'leisure', amount: 0, color: this.categoryColors['leisure'] },
      { category: 'medicines', amount: 0, color: this.categoryColors['medicines'] }
    ];
  }

  /**
   * Load expenses from localStorage - USER-SPECIFIC
   */
  private loadExpensesFromStorage(): CategoryExpense[] {
    try {
      const storageKey = this.getUserStorageKey();
      if (!storageKey) {
        console.log('ExpenseTracker: No authenticated user, returning default expenses');
        return this.getDefaultExpenses();
      }

      const stored = localStorage.getItem(storageKey);
      console.log(`ExpenseTracker: Loading expenses from key: ${storageKey}`);

      if (stored) {
        const expenses = JSON.parse(stored);
        console.log(`ExpenseTracker: Loaded expenses:`, expenses);
        return expenses;
      } else {
        console.log(`ExpenseTracker: No stored expenses found for user, returning defaults`);
      }
    } catch (error) {
      console.error('Error loading expenses from storage:', error);
    }

    // Return default empty expenses for each category
    return this.getDefaultExpenses();
  }

  /**
   * Save expenses to localStorage - USER-SPECIFIC
   */
  private saveExpensesToStorage(expenses: CategoryExpense[]): void {
    try {
      const storageKey = this.getUserStorageKey();
      if (!storageKey) {
        console.error('ExpenseTracker: Cannot save expenses - no authenticated user');
        return;
      }

      localStorage.setItem(storageKey, JSON.stringify(expenses));
      console.log(`ExpenseTracker: Saved expenses to key: ${storageKey}`, expenses);
    } catch (error) {
      console.error('Error saving expenses to storage:', error);
    }
  }

  /**
   * Add expense to a category based on tags - USER-SPECIFIC
   */
  addExpenseFromTags(tags: string[], amount: number): void {
    // Check if user is authenticated
    const currentUser = this.authService.currentUser();
    if (!currentUser?.email) {
      console.error('ExpenseTracker: Cannot add expense - no authenticated user');
      return;
    }

    const currentExpenses = this.categoryExpensesSubject.value;
    const updatedExpenses = [...currentExpenses];

    // Determine the primary category from tags (only add amount once)
    let targetCategory = this.determinePrimaryCategory(tags);

    if (targetCategory) {
      // Find the category and add the amount (only once per purchase)
      const categoryIndex = updatedExpenses.findIndex(exp => exp.category === targetCategory);
      if (categoryIndex !== -1) {
        updatedExpenses[categoryIndex].amount += amount;
      } else if (this.categoryColors[targetCategory]) {
        // Add new category if it doesn't exist but has a defined color
        updatedExpenses.push({
          category: targetCategory,
          amount: amount,
          color: this.categoryColors[targetCategory]
        });
      }

      console.log(`ExpenseTracker: Added ${amount} to ${targetCategory} for user: ${currentUser.email}`);

      // Update the subject and save to storage
      this.categoryExpensesSubject.next(updatedExpenses);
      this.saveExpensesToStorage(updatedExpenses);
    }
  }

  /**
   * Determine the primary category from tags (priority-based)
   */
  private determinePrimaryCategory(tags: string[]): string | null {
    const normalizedTags = tags.map(tag => tag.toLowerCase().replace('#', ''));

    // Priority order: medicines/healthy > food > clothes > leisure
    if (normalizedTags.includes('medicines') || normalizedTags.includes('healthy')) {
      return 'medicines';
    } else if (normalizedTags.includes('food')) {
      return 'food';
    } else if (normalizedTags.includes('clothes')) {
      return 'clothes';
    } else if (normalizedTags.includes('leisure')) {
      return 'leisure';
    }

    // If no recognized category, try to use the first tag that has a defined color
    for (const tag of normalizedTags) {
      if (this.categoryColors[tag]) {
        return tag;
      }
    }

    return null;
  }

  /**
   * Get current category expenses
   */
  getCurrentExpenses(): CategoryExpense[] {
    return this.categoryExpensesSubject.value;
  }

  /**
   * Force reload expenses for current user
   */
  reloadExpenses(): void {
    const currentUser = this.authService.currentUser();
    if (currentUser?.email) {
      console.log('ExpenseTracker: Force reloading expenses for:', currentUser.email);
      const userExpenses = this.loadExpensesFromStorage();
      this.categoryExpensesSubject.next(userExpenses);
    } else {
      console.log('ExpenseTracker: No user, loading default expenses');
      const defaultExpenses = this.getDefaultExpenses();
      this.categoryExpensesSubject.next(defaultExpenses);
    }
  }

  /**
   * Reset all expenses (for testing purposes) - USER-SPECIFIC
   */
  resetExpenses(): void {
    const currentUser = this.authService.currentUser();
    if (!currentUser?.email) {
      console.error('ExpenseTracker: Cannot reset expenses - no authenticated user');
      return;
    }

    const resetExpenses = this.getDefaultExpenses();

    this.categoryExpensesSubject.next(resetExpenses);
    this.saveExpensesToStorage(resetExpenses);
    console.log(`ExpenseTracker: Expenses reset to zero for user: ${currentUser.email}`);
  }

  /**
   * Get chart data for the dashboard
   */
  getChartData(): { labels: string[], data: number[], colors: string[] } {
    const expenses = this.getCurrentExpenses();
    const totalExpenses = expenses.reduce((sum, exp) => sum + exp.amount, 0);

    if (totalExpenses === 0) {
      // Return equal segments when no purchases have been made
      return {
        labels: ['Food', 'Clothes', 'Leisure', 'Medicines'],
        data: [25, 25, 25, 25], // Equal percentages
        colors: ['#A78BFA', '#F9A8D4', '#60A5FA', '#FBBF24']
      };
    }

    // Show percentage data when purchases have been made
    const nonZeroExpenses = expenses.filter(exp => exp.amount > 0);
    const percentages = nonZeroExpenses.map(exp =>
      Math.round((exp.amount / totalExpenses) * 100)
    );

    return {
      labels: nonZeroExpenses.map(exp => exp.category.charAt(0).toUpperCase() + exp.category.slice(1)),
      data: percentages, // Return percentages instead of raw amounts
      colors: nonZeroExpenses.map(exp => exp.color)
    };
  }
}
