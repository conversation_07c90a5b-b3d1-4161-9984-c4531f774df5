import { Injectable, signal, computed, inject, effect } from '@angular/core';
import { Observable, of } from 'rxjs';
import { AuthService } from '../auth/auth.service';

// Complete user profile interface with all possible fields
export interface UserProfile {
  // Basic info
  fullName: string;
  email: string;
  birthday: string;
  phoneNumber: string;
  governorate: string;
  gender: string;
  address: string;
  
  // Avatar data
  avatar: string;
  hasCustomAvatar: boolean;
  avatarPositionY: number;
  
  // Occupation data
  role: string;
  occupationType: 'Working' | 'Study' | 'Train' | 'Unemployed' | '';
  occupationPlace: string;
  
  // UI state flags
  showGenderPlaceholder: boolean;
  showAddressPlaceholder: boolean;
  showOccupationPlaceholder: boolean;
  showFullNamePlaceholder: boolean;
  
  // Additional data
  lastShoppingDate: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserDataService {
  private authService = inject(AuthService);
  
  // Private signals for user data
  private _userProfile = signal<UserProfile | null>(null);
  private _isLoading = signal<boolean>(false);
  
  // Public readonly signals
  readonly userProfile = this._userProfile.asReadonly();
  readonly isLoading = this._isLoading.asReadonly();
  
  // Computed signals for easy access to specific fields
  readonly fullName = computed(() => this._userProfile()?.fullName || '');
  readonly email = computed(() => this._userProfile()?.email || '');
  readonly birthday = computed(() => this._userProfile()?.birthday || '');
  readonly phoneNumber = computed(() => this._userProfile()?.phoneNumber || '');
  readonly governorate = computed(() => this._userProfile()?.governorate || '');
  readonly gender = computed(() => this._userProfile()?.gender || '');
  readonly address = computed(() => this._userProfile()?.address || '');
  readonly avatar = computed(() => this._userProfile()?.avatar || 'assets/images/default-avatar.svg');
  readonly hasCustomAvatar = computed(() => this._userProfile()?.hasCustomAvatar || false);
  readonly avatarPositionY = computed(() => this._userProfile()?.avatarPositionY || 0);
  readonly role = computed(() => this._userProfile()?.role || '');
  readonly occupationType = computed(() => this._userProfile()?.occupationType || '');
  readonly occupationPlace = computed(() => this._userProfile()?.occupationPlace || '');
  readonly lastShoppingDate = computed(() => this._userProfile()?.lastShoppingDate || 'Yesterday');
  
  // Placeholder flags
  readonly showGenderPlaceholder = computed(() => this._userProfile()?.showGenderPlaceholder ?? true);
  readonly showAddressPlaceholder = computed(() => this._userProfile()?.showAddressPlaceholder ?? true);
  readonly showOccupationPlaceholder = computed(() => this._userProfile()?.showOccupationPlaceholder ?? true);
  readonly showFullNamePlaceholder = computed(() => this._userProfile()?.showFullNamePlaceholder ?? true);

  constructor() {
    // Watch for auth user changes and load corresponding profile using effect
    effect(() => {
      const authUser = this.authService.currentUser();
      console.log('UserDataService effect triggered - authUser:', authUser);
      if (authUser) {
        // Always load the profile for the current authenticated user
        this.loadUserProfile(authUser.email);
      } else {
        this._userProfile.set(null);
        console.log('No auth user, profile set to null');
      }
    }, { allowSignalWrites: true });
  }

  /**
   * Load user profile data for a specific email
   */
  private loadUserProfile(email: string): void {
    console.log('Loading user profile for email:', email);
    this._isLoading.set(true);

    if (!email) {
      console.error('Cannot load profile: No email provided');
      this._isLoading.set(false);
      return;
    }

    try {
      // Get user-specific profile data
      const userProfileData = this.getUserProfileFromStorage(email);
      const authUser = this.authService.currentUser();

      console.log('Existing profile data:', userProfileData);
      console.log('Current auth user for merging:', authUser);

      if (userProfileData) {
        // Always merge with auth data to ensure consistency
        const mergedProfile: UserProfile = {
          ...userProfileData,
          // Prioritize auth data over stored profile data
          fullName: authUser?.full_name || userProfileData.fullName,
          email: email,
          birthday: authUser?.supInfo?.birthday || userProfileData.birthday,
          phoneNumber: authUser?.supInfo?.phoneNumber || userProfileData.phoneNumber,
          gender: this.capitalizeGender(authUser?.supInfo?.gender) || userProfileData.gender,
          avatar: authUser?.avatar || userProfileData.avatar,
          hasCustomAvatar: authUser?.hasCustomAvatar || userProfileData.hasCustomAvatar,
          avatarPositionY: authUser?.avatarPositionY || userProfileData.avatarPositionY,
          address: (authUser as any)?.address || userProfileData.address,
          governorate: (authUser as any)?.governorate || userProfileData.governorate,
          occupationType: (authUser as any)?.occupationType || userProfileData.occupationType,
          occupationPlace: (authUser as any)?.occupationPlace || userProfileData.occupationPlace,
          role: (authUser as any)?.role || userProfileData.role,
          showGenderPlaceholder: !authUser?.supInfo?.gender && userProfileData.showGenderPlaceholder,
          showAddressPlaceholder: !userProfileData.address && userProfileData.showAddressPlaceholder,
          showOccupationPlaceholder: !userProfileData.occupationType && userProfileData.showOccupationPlaceholder,
          showFullNamePlaceholder: !authUser?.full_name && userProfileData.showFullNamePlaceholder,
          lastShoppingDate: userProfileData.lastShoppingDate
        };

        this._userProfile.set(mergedProfile);
        this.saveUserProfileToStorage(email, mergedProfile);
        console.log('User profile merged with auth data for:', email, mergedProfile);
      } else {
        // Create new profile from auth data
        const newProfile = this.createDefaultProfile(email);
        this._userProfile.set(newProfile);
        this.saveUserProfileToStorage(email, newProfile);
        console.log('New user profile created for:', email, newProfile);
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
    } finally {
      this._isLoading.set(false);
    }
  }

  /**
   * Create default profile for a new user
   */
  /**
   * Helper method to extract profile data from auth user data
   */
  private extractProfileFromAuthData(userData: any, email: string): UserProfile {
    console.log('=== EXTRACTING PROFILE FROM AUTH DATA ===');
    console.log('UserData:', userData);
    console.log('UserData supInfo:', userData.supInfo);
    console.log('=== END EXTRACT PROFILE ===');

    return {
      fullName: userData.full_name || '',
      email: email,
      birthday: userData.supInfo?.birthday || userData.birthday || '',
      phoneNumber: userData.supInfo?.phoneNumber || userData.phoneNumber || '+216 00 000 000',
      governorate: userData.governorate || 'Tunisie',
      gender: this.capitalizeGender(userData.supInfo?.gender || userData.gender) || '',
      address: userData.address || '',
      avatar: userData.avatar || 'assets/images/default-avatar.svg',
      hasCustomAvatar: userData.hasCustomAvatar || false,
      avatarPositionY: userData.avatarPositionY || 0,
      role: userData.role || '',
      occupationType: userData.occupationType || '',
      occupationPlace: userData.occupationPlace || '',
      showGenderPlaceholder: !(userData.supInfo?.gender || userData.gender),
      showAddressPlaceholder: !userData.address,
      showOccupationPlaceholder: !userData.occupationType,
      showFullNamePlaceholder: !userData.full_name,
      lastShoppingDate: 'Yesterday'
    };
  }

  /**
   * Update registered user data with profile changes
   */
  private updateRegisteredUserWithProfile(email: string, profile: UserProfile): void {
    try {
      const registeredUsersStr = localStorage.getItem('registeredUsers');
      if (registeredUsersStr) {
        const registeredUsers = JSON.parse(registeredUsersStr);
        if (registeredUsers[email]) {
          // Update the registered user data with profile data
          registeredUsers[email] = {
            ...registeredUsers[email],
            full_name: profile.fullName,
            avatar: profile.avatar,
            hasCustomAvatar: profile.hasCustomAvatar,
            avatarPositionY: profile.avatarPositionY,
            birthday: profile.birthday,
            phoneNumber: profile.phoneNumber,
            gender: profile.gender,
            address: profile.address,
            governorate: profile.governorate,
            occupationType: profile.occupationType,
            occupationPlace: profile.occupationPlace,
            role: profile.role
          };
          localStorage.setItem('registeredUsers', JSON.stringify(registeredUsers));
          console.log('Updated registered user data with profile changes for:', email);
        }
      }
    } catch (error) {
      console.error('Error updating registered user data with profile:', error);
    }
  }

  private createDefaultProfile(email: string): UserProfile {
    // Get basic info from AuthService if available
    const authUser = this.authService.currentUser();

    console.log('=== CREATING DEFAULT PROFILE ===');
    console.log('Email:', email);
    console.log('AuthUser:', authUser);
    console.log('AuthUser supInfo:', authUser?.supInfo);
    console.log('=== END CREATE DEFAULT PROFILE ===');

    return {
      fullName: authUser?.full_name || '',
      email: email,
      birthday: authUser?.supInfo?.birthday || (authUser as any)?.birthday || '',
      phoneNumber: authUser?.supInfo?.phoneNumber || (authUser as any)?.phoneNumber || '+216 00 000 000',
      governorate: (authUser as any)?.governorate || 'Tunisie',
      gender: this.capitalizeGender(authUser?.supInfo?.gender || (authUser as any)?.gender) || '',
      address: (authUser as any)?.address || '',
      avatar: authUser?.avatar || 'assets/images/default-avatar.svg',
      hasCustomAvatar: authUser?.hasCustomAvatar || false,
      avatarPositionY: authUser?.avatarPositionY || 0,
      role: (authUser as any)?.role || '',
      occupationType: (authUser as any)?.occupationType || '',
      occupationPlace: (authUser as any)?.occupationPlace || '',
      showGenderPlaceholder: !(authUser?.supInfo?.gender || (authUser as any)?.gender),
      showAddressPlaceholder: !(authUser as any)?.address,
      showOccupationPlaceholder: !(authUser as any)?.occupationType,
      showFullNamePlaceholder: !authUser?.full_name,
      lastShoppingDate: 'Yesterday'
    };
  }

  /**
   * Get user profile from localStorage
   */
  private getUserProfileFromStorage(email: string): UserProfile | null {
    try {
      if (!email) {
        console.error('Cannot get profile from storage: No email provided');
        return null;
      }

      const storageKey = `userProfile_${email}`;
      const storedProfile = localStorage.getItem(storageKey);
      
      if (storedProfile) {
        console.log(`Found profile data in storage for ${email}`);
        return JSON.parse(storedProfile) as UserProfile;
      }
      
      // Try to find data in the registeredUsers as a fallback
      const registeredUsersStr = localStorage.getItem('registeredUsers');
      if (registeredUsersStr) {
        const registeredUsers = JSON.parse(registeredUsersStr);
        if (registeredUsers[email]) {
          console.log(`Found user data in registeredUsers for ${email}`);
          // Extract profile data from registeredUsers
          const userData = registeredUsers[email];
          return this.extractProfileFromAuthData(userData, email);
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error loading user profile from storage:', error);
      return null;
    }
  }

  /**
   * Save user profile to localStorage
   */
  private saveUserProfileToStorage(email: string, profile: UserProfile): void {
    try {
      if (!email) {
        console.error('Cannot save profile to storage: No email provided');
        return;
      }

      const storageKey = `userProfile_${email}`;
      localStorage.setItem(storageKey, JSON.stringify(profile));
      console.log('User profile saved for:', email);
      
      // Also update the registeredUsers data to keep them in sync
      this.updateRegisteredUserWithProfile(email, profile);
    } catch (error) {
      console.error('Error saving user profile to storage:', error);
    }
  }

  /**
   * Update user profile field
   */
  updateField<K extends keyof UserProfile>(field: K, value: UserProfile[K]): Observable<UserProfile> {
    const currentProfile = this._userProfile();
    const currentUser = this.authService.currentUser();
    
    if (!currentProfile || !currentUser) {
      return of(null as any);
    }

    const updatedProfile: UserProfile = {
      ...currentProfile,
      [field]: value
    };

    // Update signal
    this._userProfile.set(updatedProfile);
    
    // Save to localStorage
    this.saveUserProfileToStorage(currentUser.email, updatedProfile);
    
    // Also update AuthService if it's a field that exists there
    if (field === 'fullName') {
      this.authService.updateUser({ full_name: value as string });
    } else if (field === 'email') {
      this.authService.updateUser({ email: value as string });
    } else if (field === 'avatar' || field === 'hasCustomAvatar' || field === 'avatarPositionY') {
      this.authService.updateUser({
        [field]: value,
        avatar: updatedProfile.avatar,
        hasCustomAvatar: updatedProfile.hasCustomAvatar,
        avatarPositionY: updatedProfile.avatarPositionY
      } as any);
    } else if (field === 'address' || field === 'occupationType' || field === 'occupationPlace' || field === 'role' || field === 'governorate') {
      // Save address, occupation, and governorate data to AuthService for persistence
      this.authService.updateUser({
        [field]: value,
        address: updatedProfile.address,
        occupationType: updatedProfile.occupationType,
        occupationPlace: updatedProfile.occupationPlace,
        role: updatedProfile.role,
        governorate: updatedProfile.governorate
      } as any);
    } else if (field === 'birthday' || field === 'phoneNumber' || field === 'gender') {
      // Save supplementary info to AuthService using supInfo structure
      const currentAuthUser = this.authService.currentUser();
      if (currentAuthUser) {
        this.authService.updateUser({
          supInfo: {
            ...currentAuthUser.supInfo,
            birthday: updatedProfile.birthday,
            phoneNumber: updatedProfile.phoneNumber,
            gender: updatedProfile.gender as 'male' | 'female' | 'other'
          }
        } as any);
      }
    }

    console.log(`Updated ${field} for user ${currentUser.email}:`, value);
    return of(updatedProfile);
  }

  /**
   * Update multiple fields at once
   */
  updateProfile(updates: Partial<UserProfile>): Observable<UserProfile> {
    const currentProfile = this._userProfile();
    const currentUser = this.authService.currentUser();
    
    if (!currentProfile || !currentUser) {
      return of(null as any);
    }

    const updatedProfile: UserProfile = {
      ...currentProfile,
      ...updates
    };

    // Update signal
    this._userProfile.set(updatedProfile);
    
    // Save to localStorage
    this.saveUserProfileToStorage(currentUser.email, updatedProfile);
    
    // Update AuthService with relevant fields
    const authUpdates: any = {};
    if (updates.fullName) authUpdates.full_name = updates.fullName;
    if (updates.email) authUpdates.email = updates.email;
    if (updates.avatar) authUpdates.avatar = updates.avatar;
    if (updates.hasCustomAvatar !== undefined) authUpdates.hasCustomAvatar = updates.hasCustomAvatar;
    if (updates.avatarPositionY !== undefined) authUpdates.avatarPositionY = updates.avatarPositionY;
    if (updates.address !== undefined) authUpdates.address = updates.address;
    if (updates.occupationType !== undefined) authUpdates.occupationType = updates.occupationType;
    if (updates.occupationPlace !== undefined) authUpdates.occupationPlace = updates.occupationPlace;
    if (updates.role !== undefined) authUpdates.role = updates.role;
    if (updates.governorate !== undefined) authUpdates.governorate = updates.governorate;

    // Handle supInfo fields separately
    const supInfoUpdates: any = {};
    if (updates.birthday !== undefined) supInfoUpdates.birthday = updates.birthday;
    if (updates.phoneNumber !== undefined) supInfoUpdates.phoneNumber = updates.phoneNumber;
    if (updates.gender !== undefined) supInfoUpdates.gender = updates.gender;

    if (Object.keys(supInfoUpdates).length > 0) {
      const currentAuthUser = this.authService.currentUser();
      if (currentAuthUser) {
        authUpdates.supInfo = {
          ...currentAuthUser.supInfo,
          ...supInfoUpdates
        };
      }
    }

    if (Object.keys(authUpdates).length > 0) {
      this.authService.updateUser(authUpdates);
    }

    console.log(`Updated profile for user ${currentUser.email}:`, updates);
    return of(updatedProfile);
  }

  /**
   * Clear user data (for logout)
   */
  clearUserData(): void {
    // Only clear the in-memory state, don't remove localStorage data
    // This ensures we don't lose data when switching between accounts
    this._userProfile.set(null);
    console.log('User data cleared from memory only');
  }

  /**
   * Force reload user profile from storage
   */
  reloadProfile(): void {
    const currentUser = this.authService.currentUser();
    if (currentUser) {
      this.loadUserProfile(currentUser.email);
    }
  }

  /**
   * Helper method to capitalize gender values from sup-info
   */
  private capitalizeGender(gender: string | undefined): string {
    if (!gender) return '';

    switch (gender.toLowerCase()) {
      case 'male':
        return 'Male';
      case 'female':
        return 'Female';
      case 'other':
        return 'Other';
      default:
        return gender;
    }
  }
}
