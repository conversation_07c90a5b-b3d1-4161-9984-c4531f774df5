import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ImageOptimizationService {
  private imageCache = new Map<string, HTMLImageElement>();
  private loadingPromises = new Map<string, Promise<HTMLImageElement>>();
  private intersectionObserver?: IntersectionObserver;
  private preloadQueue = new Set<string>();

  constructor() {
    this.setupIntersectionObserver();
  }

  /**
   * Setup intersection observer for lazy loading
   */
  private setupIntersectionObserver(): void {
    if ('IntersectionObserver' in window) {
      this.intersectionObserver = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement;
              const src = img.dataset['src'];
              if (src) {
                this.loadImage(src).then(loadedImg => {
                  img.src = loadedImg.src;
                  img.classList.add('loaded');
                  this.intersectionObserver?.unobserve(img);
                }).catch(() => {
                  img.src = 'assets/images/default-brand-logo.png';
                  img.classList.add('error');
                });
              }
            }
          });
        },
        {
          rootMargin: '50px 0px',
          threshold: 0.1
        }
      );
    }
  }

  /**
   * Preload critical images
   */
  preloadImages(urls: string[]): Promise<void[]> {
    const promises = urls.map(url => this.loadImage(url));
    return Promise.allSettled(promises).then(() => []);
  }

  /**
   * Load image with caching
   */
  loadImage(src: string): Promise<HTMLImageElement> {
    // Return cached image if available
    if (this.imageCache.has(src)) {
      return Promise.resolve(this.imageCache.get(src)!);
    }

    // Return existing loading promise if in progress
    if (this.loadingPromises.has(src)) {
      return this.loadingPromises.get(src)!;
    }

    // Create new loading promise
    const promise = new Promise<HTMLImageElement>((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        this.imageCache.set(src, img);
        this.loadingPromises.delete(src);
        resolve(img);
      };

      img.onerror = () => {
        this.loadingPromises.delete(src);
        reject(new Error(`Failed to load image: ${src}`));
      };

      // Set loading attribute for better performance
      img.loading = 'lazy';
      img.src = src;
    });

    this.loadingPromises.set(src, promise);
    return promise;
  }

  /**
   * Setup lazy loading for an image element
   */
  setupLazyLoading(img: HTMLImageElement, src: string): void {
    if (this.intersectionObserver) {
      img.dataset['src'] = src;
      img.classList.add('lazy-image');
      this.intersectionObserver.observe(img);
    } else {
      // Fallback for browsers without IntersectionObserver
      img.src = src;
    }
  }

  /**
   * Optimize image URL based on device capabilities
   */
  optimizeImageUrl(baseUrl: string, options?: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'avif' | 'jpg' | 'png';
  }): string {
    // For now, return the base URL
    // In a real implementation, you might use a CDN service like Cloudinary
    return baseUrl;
  }

  /**
   * Clear image cache
   */
  clearCache(): void {
    this.imageCache.clear();
    this.loadingPromises.clear();
  }

  /**
   * Get cache size
   */
  getCacheSize(): number {
    return this.imageCache.size;
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
    this.clearCache();
  }
}
