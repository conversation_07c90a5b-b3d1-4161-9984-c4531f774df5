// shopper-analytics.component.scss
// Color variables
$primary-color: #667eea;
$primary-light: #c3dafe;
$primary-dark: #4c51bf;
$danger-color: #e53e3e;
$success-color: #38a169;
$warning-color: #ed8936;
$text-primary: var(--text-color, #1a202c);
$text-secondary: var(--text-color, #4a5568);
$text-light: var(--text-color, #718096);
$border-color: var(--border-color, #e2e8f0);
$bg-light: var(--card-bg, #ffffff);
$bg-lighter: var(--primary-bg, #f7fafc);
$bg-accent: var(--secondary-bg, #ebf4ff);
$input-bg: var(--input-bg, #ffffff);
$input-text: var(--input-text, #1a202c);
$input-border: var(--input-border, #e2e8f0);

// Shadows
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.05), 0 4px 6px rgba(0, 0, 0, 0.05);
$shadow-card: 0 0 0 1px rgba(0, 0, 0, 0.05), 0 5px 15px rgba(0, 0, 0, 0.05);

// Sizing and spacing
$border-radius-sm: 6px;
$border-radius-md: 10px;
$border-radius-lg: 16px;
$border-radius-xl: 20px;
$sidebar-width: 240px;
$navbar-height: 70px;

// Transitions
$transition-fast: 150ms ease;
$transition-normal: 250ms ease;
$transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);

// Responsive breakpoints
@mixin desktop {
  @media (min-width: 1280px) {
    @content;
  }
}

@mixin laptop {
  @media (min-width: 1024px) and (max-width: 1279px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 768px) and (max-width: 1023px) {
    @content;
  }
}

@mixin mobile {
  @media (max-width: 767px) {
    @content;
  }
}

@mixin small-mobile {
  @media (max-width: 575px) {
    @content;
  }
}

// Mixins
@mixin card {
  background-color: $bg-light;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-card;
  transition: box-shadow $transition-normal, transform $transition-normal;
  overflow: hidden;

  &:hover {
    box-shadow: $shadow-lg;
  }
}

@mixin section-padding {
  padding: 24px;

  @include tablet {
    padding: 20px;
  }

  @include mobile {
    padding: 16px;
  }
}

// Main container layout
.analytics-container {
  margin-left: $sidebar-width;
  padding: 30px;
  padding-top: calc(#{$navbar-height} + 24px);
  min-height: 100vh;
  background-color: var(--primary-bg, $bg-lighter);
  display: flex;
  justify-content: center;
  transition: margin-left $transition-normal, background-color $transition-normal;

  @include tablet {
    margin-left: 80px; // Collapsed sidebar width
    padding: 24px;
    padding-top: calc(#{$navbar-height} + 20px);
  }

  @include mobile {
    margin-left: 0;
    padding: 16px;
    padding-top: calc(#{$navbar-height} + 16px);
  }
}

// Analytics card - Clean design inspired by shopper-dashboard
.analytics-card {
  width: 100%;
  max-width: 1100px;
  @include section-padding;
  background: var(--card-bg, white);
  border-radius: $border-radius-lg;
  border: 1px solid var(--border-color, #e0e0e0);
  box-shadow: var(--shadow-sm, 0 2px 10px rgba(0, 0, 0, 0.1));
  transition: all $transition-normal;

  &:hover {
    box-shadow: var(--shadow-md, 0 8px 16px rgba(0, 0, 0, 0.15));
  }

  // Dark mode styles
  :host-context([data-theme="dark"]) & {
    background: var(--card-bg, #2d2d2d);
    border-color: var(--border-color, #404040);
  }

  // Dashboard header - Clean design inspired by shopper-dashboard
  .dashboard-header {
    background: var(--card-bg, white);
    border-radius: $border-radius-lg;
    border: 1px solid var(--border-color, #e0e0e0);
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: var(--shadow-sm, 0 2px 10px rgba(0, 0, 0, 0.1));

    display: flex;
    justify-content: space-between;
    align-items: center;

    .dashboard-title {
      font-size: 2rem;
      font-weight: 800;
      color: var(--text-color, $text-primary);
      margin: 0;
      letter-spacing: -0.8px;
      background: linear-gradient(135deg, #6B48FF, #A855F7, #B794F4);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;

      // Fallback for browsers that don't support background-clip
      @supports not (-webkit-background-clip: text) {
        color: #6B48FF;
      }

      @include mobile {
        font-size: 1.5rem;
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 24px;

      .date-range {
        display: flex;
        align-items: center;
        background: var(--secondary-bg, #F9FAFB);
        border: 1px solid var(--border-color, #E5E7EB);
        border-radius: $border-radius-md;
        padding: 12px 20px;
        font-size: 0.95rem;
        font-weight: 600;
        color: var(--text-color, $text-secondary);
        transition: all $transition-normal;
        cursor: pointer;

        .icon {
          margin-right: 10px;
          color: #6B48FF;
          font-size: 1.1rem;
        }

        &:hover {
          background: var(--hover-bg-light, #F3F4F6);
          transform: translateY(-1px);
        }
      }

      .test-buttons {
        display: flex;
        gap: 8px;

        .test-btn {
          padding: 8px 12px;
          border: none;
          border-radius: $border-radius-sm;
          font-size: 0.85rem;
          font-weight: 600;
          cursor: pointer;
          transition: all $transition-fast;
          display: flex;
          align-items: center;
          gap: 4px;

          &.add-data {
            background: linear-gradient(135deg, #48BB78, #38A169);
            color: white;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
            }
          }

          &.clear-data {
            background: linear-gradient(135deg, #F56565, #E53E3E);
            color: white;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(245, 101, 101, 0.3);
            }
          }

          &.refresh-data {
            background: linear-gradient(135deg, #6B48FF, #A855F7);
            color: white;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(107, 72, 255, 0.3);
            }
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }

    // Dark mode styles
    :host-context([data-theme="dark"]) & {
      background: var(--card-bg, #2d2d2d);
      border-color: var(--border-color, #404040);

      .header-actions {
        .date-range {
          background: var(--secondary-bg, #374151);
          border-color: var(--border-color, #4B5563);
          color: var(--text-color, white);

          &:hover {
            background: var(--hover-bg-light, #4B5563);
          }
        }
      }
    }

    // Mobile responsive styles
    @include mobile {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .dashboard-title {
        font-size: 1.5rem;
      }

      .header-actions {
        width: 100%;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 12px;

        .test-buttons {
          flex-wrap: wrap;
          gap: 6px;

          .test-btn {
            padding: 6px 10px;
            font-size: 0.8rem;
          }
        }
      }
    }
  }

  // Spending section - Clean design inspired by shopper-dashboard
  .spending-section {
    background: var(--card-bg, white);
    border-radius: $border-radius-lg;
    border: 1px solid var(--border-color, #e0e0e0);
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: var(--shadow-sm, 0 2px 10px rgba(0, 0, 0, 0.1));
    transition: all $transition-normal;

    display: flex;
    justify-content: space-between;
    align-items: center;

    &:hover {
      box-shadow: var(--shadow-md, 0 8px 16px rgba(0, 0, 0, 0.15));
    }

    // Dark mode styles
    :host-context([data-theme="dark"]) & {
      background: var(--card-bg, #2d2d2d);
      border-color: var(--border-color, #404040);
    }
  }

  // Stats grid - Clean design inspired by shopper-dashboard
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 32px;

    .stat-item {
      background: var(--card-bg, white);
      border-radius: $border-radius-lg;
      border: 1px solid var(--border-color, #e0e0e0);
      padding: 28px;
      box-shadow: var(--shadow-sm, 0 2px 10px rgba(0, 0, 0, 0.1));
      transition: all $transition-normal;
      position: relative;

      // Color indicator bar
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(180deg, #6B48FF, #A855F7);
        z-index: 1;
      }

      &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-md, 0 8px 16px rgba(0, 0, 0, 0.15));
      }

      .stat-label {
        font-size: 0.85rem;
        font-weight: 700;
        color: var(--text-color, $text-secondary);
        margin-bottom: 12px;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .stat-value {
        font-size: 1.8rem;
        font-weight: 800;
        color: var(--text-color, $text-primary);
        margin-bottom: 8px;
        background: linear-gradient(135deg, #6B48FF, #A855F7);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;

        // Fallback for browsers that don't support background-clip
        @supports not (-webkit-background-clip: text) {
          color: #6B48FF;
        }
      }

      .stat-change {
        margin-top: 12px;
        font-size: 0.9rem;
        font-weight: 600;
        display: flex;
        align-items: center;

        &.positive {
          color: #48BB78;
        }

        &.negative {
          color: #F56565;
        }

        .icon {
          margin-right: 6px;
          font-size: 1rem;
        }
      }

      // Dark mode styles
      :host-context([data-theme="dark"]) & {
        background: var(--card-bg, #2d2d2d);
        border-color: var(--border-color, #404040);
      }
    }

    @include tablet {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
    }

    @include mobile {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }

  // Category and Product expenses cards - Clean design inspired by shopper-dashboard
  .category-expenses-card, .product-expenses-card {
    background: var(--card-bg, white);
    border-radius: $border-radius-lg;
    border: 1px solid var(--border-color, #e0e0e0);
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: var(--shadow-sm, 0 2px 10px rgba(0, 0, 0, 0.1));
    transition: all $transition-normal;

    &:hover {
      transform: translateY(-4px);
      box-shadow: var(--shadow-md, 0 8px 16px rgba(0, 0, 0, 0.15));
    }

    .card-header {
      margin-bottom: 24px;
      padding: 0;
      background: transparent;
      border: none;

      .card-title {
        font-size: 1.4rem;
        font-weight: 800;
        color: var(--text-color, $text-primary);
        margin: 0;
        background: linear-gradient(135deg, #6B48FF, #A855F7);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;

        // Fallback for browsers that don't support background-clip
        @supports not (-webkit-background-clip: text) {
          color: #6B48FF;
        }
      }
    }

    // Dark mode styles
    :host-context([data-theme="dark"]) & {
      background: var(--card-bg, #2d2d2d);
      border-color: var(--border-color, #404040);
    }
  }

  // Progress cards - Clean design inspired by shopper-dashboard
  .savings-goal-progress, .budget-spent-progress {
    background: var(--card-bg, white);
    border-radius: $border-radius-lg;
    border: 1px solid var(--border-color, #e0e0e0);
    padding: 28px;
    box-shadow: var(--shadow-sm, 0 2px 10px rgba(0, 0, 0, 0.1));
    transition: all $transition-normal;
    position: relative;

    &:hover {
      transform: translateY(-4px);
      box-shadow: var(--shadow-md, 0 8px 16px rgba(0, 0, 0, 0.15));
    }

    // Dark mode styles
    :host-context([data-theme="dark"]) & {
      background: var(--card-bg, #2d2d2d);
      border-color: var(--border-color, #404040);
    }
  }

  .savings-goal-progress {
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #6B48FF, #A855F7);
      z-index: 1;
    }
  }

  .budget-spent-progress {
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #F6AD55, #ED8936);
      z-index: 1;
    }
  }
}

// Progress section
.progress-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;

  @include tablet {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
  }

  @include mobile {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

// Spending info
.spending-info {
  flex: 1;

  @include mobile {
    width: 100%;
    text-align: center;
    margin-bottom: 20px;
  }
}

// Spending amount - Modern redesign
.spending-amount {
  font-size: 3rem;
  font-weight: 800;
  color: var(--text-color, $text-primary);
  letter-spacing: -0.03em;
  display: flex;
  align-items: baseline;
  background: linear-gradient(135deg, #6B48FF, #A855F7, #B794F4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  // Fallback for browsers that don't support background-clip
  @supports not (-webkit-background-clip: text) {
    color: #6B48FF;
  }

  @include mobile {
    font-size: 2.2rem;
    justify-content: center;
  }

  .currency {
    color: var(--text-color, $text-secondary);
    font-size: 0.4em;
    margin-left: 8px;
    font-weight: 700;
    opacity: 0.8;
  }
}

// Spending change - Modern redesign
.spending-change {
  margin: 16px 0;
  font-size: 1.1rem;
  display: flex;
  align-items: center;

  @include mobile {
    justify-content: center;
  }

  .percentage {
    display: inline-flex;
    align-items: center;
    font-weight: 700;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.95rem;
    transition: all $transition-normal;

    &.increase {
      color: #F56565;
      background: rgba(245, 101, 101, 0.1);
      border: 1px solid rgba(245, 101, 101, 0.2);

      &::before {
        content: "↗";
        margin-right: 6px;
        font-size: 1.1rem;
      }

      &:hover {
        background: rgba(245, 101, 101, 0.15);
        transform: translateY(-1px);
      }
    }

    &.decrease {
      color: #48BB78;
      background: rgba(72, 187, 120, 0.1);
      border: 1px solid rgba(72, 187, 120, 0.2);

      &::before {
        content: "↘";
        margin-right: 6px;
        font-size: 1.1rem;
      }

      &:hover {
        background: rgba(72, 187, 120, 0.15);
        transform: translateY(-1px);
      }
    }
  }

  .compared-period {
    margin-left: 12px;
    color: var(--text-color, $text-light);
    font-size: 0.95rem;
    font-weight: 500;
    opacity: 0.8;
  }
}

// Spending label - Modern redesign
.spending-label {
  color: var(--text-color, $text-secondary);
  font-size: 1.1rem;
  margin-top: 12px;
  font-weight: 600;
  opacity: 0.9;
}

// Trend chart container - Clean design inspired by shopper-dashboard
.trend-chart-container {
  width: 200px;
  height: 90px;
  padding: 12px;
  background: var(--secondary-bg, #F9FAFB);
  border: 1px solid var(--border-color, #E5E7EB);
  border-radius: $border-radius-md;

  @include tablet {
    width: 180px;
    height: 80px;
  }

  @include mobile {
    width: 100%;
    height: 140px;
  }

  // Dark mode styles
  :host-context([data-theme="dark"]) & {
    background: var(--secondary-bg, #374151);
    border-color: var(--border-color, #4B5563);
  }
}

// Expense list sections
app-expense-list {
  margin-bottom: 32px;
  display: block;

  &:last-of-type {
    margin-bottom: 16px;
  }

  // Add style for section headers
  ::ng-deep .expense-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      font-size: 1.1rem;
      font-weight: 600;
      color: $text-primary;
    }

    .toggle-btn {
      background: none;
      border: none;
      color: $primary-color;
      cursor: pointer;
      font-size: 0.9rem;
      display: flex;
      align-items: center;

      &:hover {
        text-decoration: underline;
      }

      .icon {
        margin-left: 4px;
      }
    }
  }
}

// Summary value
.summary-value {
  text-align: center;
  font-size: 1.5rem;
  font-weight: 600;
  color: $text-primary;
  margin-top: 32px;
  padding: 16px;
  background-color: $bg-lighter;
  border-radius: $border-radius-md;

  @include mobile {
    font-size: 1.25rem;
    margin-top: 24px;
    padding: 12px;
  }
}

// Loading container - Modern redesign
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: var(--text-color, $text-secondary);
  position: relative;

  .loading-spinner {
    width: 56px;
    height: 56px;
    border: 4px solid rgba(171, 85, 247, 0.1);
    border-top: 4px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 24px;
    position: relative;

    // Gradient border effect
    &::before {
      content: '';
      position: absolute;
      top: -4px;
      left: -4px;
      right: -4px;
      bottom: -4px;
      background: linear-gradient(45deg, #6B48FF, #A855F7, #B794F4, #6B48FF);
      border-radius: 50%;
      z-index: -1;
      animation: spin 2s linear infinite reverse;
    }

    box-shadow:
      0 8px 25px rgba(107, 72, 255, 0.2),
      0 4px 10px rgba(168, 85, 247, 0.1);
  }

  div {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color, $text-secondary);
    text-align: center;
    background: linear-gradient(135deg, #6B48FF, #A855F7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;

    // Fallback for browsers that don't support background-clip
    @supports not (-webkit-background-clip: text) {
      color: #6B48FF;
    }
  }

  // Dark mode styles
  :host-context([data-theme="dark"]) & {
    .loading-spinner {
      border-color: rgba(171, 85, 247, 0.15);
      box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        0 4px 10px rgba(107, 72, 255, 0.2);
    }
  }
}

// Animations
@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

// Fade in animation
.fade-in {
  animation: fadeIn 0.4s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Stats grid
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;

  .stat-item {
    background-color: $bg-light;
    border-radius: $border-radius-md;
    padding: 16px;
    border: 1px solid $border-color;
    transition: transform $transition-fast;

    &:hover {
      transform: translateY(-2px);
    }

    .stat-label {
      font-size: 0.9rem;
      font-weight: 600;
      color: $text-primary;
      margin-bottom: 8px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .stat-value {
      font-size: 1.5rem;
      font-weight: 700;
      color: $text-primary;
      margin-bottom: 4px;
    }

    .stat-change {
      margin-top: 8px;
      font-size: 0.85rem;
      display: flex;
      align-items: center;

      &.positive {
        color: $success-color;
      }

      &.negative {
        color: $danger-color;
      }

      .icon {
        margin-right: 4px;
      }
    }
  }

  @include mobile {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

// Data cards for customization
.data-card {
  background-color: $bg-light;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  padding: 20px;
  margin-bottom: 20px;
  border-left: 4px solid transparent;

  &.primary {
    border-left-color: $primary-color;
  }

  &.success {
    border-left-color: $success-color;
  }

  &.warning {
    border-left-color: $warning-color;
  }

  &.danger {
    border-left-color: $danger-color;
  }

  .data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .data-title {
      font-weight: 600;
      color: $text-primary;
      display: flex;
      align-items: center;

      .icon {
        margin-right: 8px;
      }
    }
  }

  @include mobile {
    padding: 16px;
  }
}

:host ::ng-deep {
  .savings-goal-progress {
    .percentage {
      font-weight: 800 !important;
      font-size: 1.2rem !important;
      color: #ffffff !important;
      background-color: #6B48FF !important;
      padding: 6px 16px !important;
      border-radius: 20px !important;
      display: inline-block !important;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
      box-shadow: 0 2px 4px rgba(107, 72, 255, 0.3) !important;
    }
  }

  .budget-spent-progress {
    .percentage {
      font-weight: 800 !important;
      font-size: 1.2rem !important;
      color: #ffffff !important;
      background-color: #48BB78 !important;
      padding: 6px 16px !important;
      border-radius: 20px !important;
      display: inline-block !important;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
      box-shadow: 0 2px 4px rgba(72, 187, 120, 0.3) !important;
    }
  }

  // Enhanced styles for expense lists
  .category-expenses-card, .product-expenses-card {
    .card-title {
      color: var(--text-color, #6B48FF) !important;
      font-weight: 800 !important;
      font-size: 1.4rem !important;
      background: linear-gradient(135deg, #6B48FF, #A855F7) !important;
      -webkit-background-clip: text !important;
      -webkit-text-fill-color: transparent !important;
      background-clip: text !important;

      // Fallback for browsers that don't support background-clip
      @supports not (-webkit-background-clip: text) {
        color: #6B48FF !important;
      }
    }

    .list-title {
      color: var(--text-color, #6B48FF) !important;
      font-weight: 700 !important;
      font-size: 1.2rem !important;
      margin-bottom: 16px !important;
      background: linear-gradient(135deg, #6B48FF, #A855F7) !important;
      -webkit-background-clip: text !important;
      -webkit-text-fill-color: transparent !important;
      background-clip: text !important;

      // Fallback for browsers that don't support background-clip
      @supports not (-webkit-background-clip: text) {
        color: #6B48FF !important;
      }
    }

    .expense-item {
      .expense-name {
        color: var(--text-color, #6B48FF) !important;
        font-weight: 700 !important;
      }

      .percentage {
        color: #6B48FF !important;
        font-weight: 700 !important;
        font-size: 0.95rem !important;

        // Dark mode support - use specific colors from memories
        :host-context([data-theme="dark"]) & {
          color: #6B48FF !important;
        }
      }

      .progress-item .percentage {
        color: #48BB78 !important;

        // Dark mode support - use specific colors from memories
        :host-context([data-theme="dark"]) & {
          color: #48BB78 !important;
        }
      }
    }
  }
}

// Authentication and error states
.auth-required,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 40px;
  text-align: center;

  .auth-message,
  .error-message {
    background: var(--card-bg, $bg-light);
    border: 1px solid var(--border-color, $border-color);
    border-radius: $border-radius-lg;
    padding: 32px;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    h3 {
      color: var(--text-color, $text-primary);
      font-size: 1.5rem;
      font-weight: 700;
      margin-bottom: 16px;
    }

    p {
      color: var(--text-color, $text-secondary);
      font-size: 1rem;
      margin-bottom: 24px;
      line-height: 1.5;
    }

    .retry-button {
      background: linear-gradient(135deg, #6B48FF, #A855F7);
      color: white;
      border: none;
      border-radius: $border-radius-md;
      padding: 12px 24px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all $transition-normal;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(107, 72, 255, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  // Dark mode styles
  :host-context([data-theme="dark"]) & {
    .auth-message,
    .error-message {
      background: var(--card-bg, #2d2d2d);
      border-color: var(--border-color, #404040);
    }
  }
}

// Additional dark mode improvements
:host-context([data-theme="dark"]) {
  .analytics-card {
    color: var(--text-color, white);

    * {
      color: inherit;
    }

    .dashboard-title,
    .stat-value,
    .spending-amount,
    .card-title,
    .list-title {
      // These elements use gradient text, so they maintain their colors
    }

    .stat-label,
    .spending-label,
    .compared-period {
      color: rgba(255, 255, 255, 0.8) !important;
    }
  }
}