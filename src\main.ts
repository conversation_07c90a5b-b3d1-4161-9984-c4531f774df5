import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig } from './app/app.config';
import { AppComponent } from './app/app.component';
import { register } from 'swiper/element/bundle';

// Performance optimization: Preload critical resources
const preloadCriticalResources = () => {
  // Preload critical images
  const criticalImages = [
    'assets/images/Recetto-Login_Register-Logo.png',
    'assets/images/default-avatar.svg',
    'assets/images/default-brand-logo.png'
  ];

  criticalImages.forEach(src => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    document.head.appendChild(link);
  });
};

// Initialize performance monitoring
const initPerformanceMonitoring = () => {
  // Mark app start time
  performance.mark('app-start');
};

// Bootstrap application with optimizations
const startApp = async () => {
  try {
    // Initialize performance monitoring
    initPerformanceMonitoring();

    // Preload critical resources
    preloadCriticalResources();

    // Register Swiper components
    register();

    // Bootstrap Angular application
    const app = await bootstrapApplication(AppComponent, appConfig);

    // Mark app ready
    performance.mark('app-ready');
    performance.measure('app-bootstrap', 'app-start', 'app-ready');

    console.log('Application bootstrapped successfully');
    return app;
  } catch (err) {
    console.error('Application bootstrap failed:', err);
    throw err;
  }
};

// Start the application
startApp();