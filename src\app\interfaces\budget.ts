export interface BudgetData {
  id: string;
  name: string;
  period: BudgetPeriod;
  amount: number;
  category: string;
  createdAt: Date;
  notifications: BudgetNotifications;
  spent?: number;
  remaining?: number;
  percentSpent?: number;
  userId: string; // Email of the user who owns this budget
}

export interface BudgetCategoryData {
  name: string;
  amount: number;
  percentOfTotal: number;
  spent?: number;
  remaining?: number;
  percentSpent?: number;
  userId: string; // Email of the user who owns this category
}

export enum BudgetPeriod {
  OneMonth = 'OneMonth',
  ThreeMonths = 'ThreeMonths',
  SixMonths = 'SixMonths',
  OneYear = 'OneYear'
}

export interface BudgetNotifications {
  budgetOverrun: boolean;
  riskOfOverrun: boolean;
}

export interface BudgetState {
  budgets: BudgetData[];
  categories: BudgetCategoryData[];
  totalBudget: number;
  totalSpent: number;
  totalRemaining: number;
  percentSpent: number;
  percentRemaining: number;
  isLoading: boolean;
}

// Type guard for BudgetData
export function isBudgetData(obj: any): obj is BudgetData {
  return obj && typeof obj === 'object' && 'id' in obj && 'name' in obj && 'userId' in obj;
}

// Type guard for BudgetCategoryData
export function isBudgetCategoryData(obj: any): obj is BudgetCategoryData {
  return obj && typeof obj === 'object' && 'name' in obj && 'amount' in obj && 'userId' in obj;
}
