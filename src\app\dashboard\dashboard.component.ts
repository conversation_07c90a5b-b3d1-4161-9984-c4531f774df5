import { Component, OnInit, HostListener, computed, inject, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SidebarService } from '../sidebar/sidebar.service';
import { DashboardDataService } from '../core/dashboard/dashboard-data.service';
import { AuthService } from '../core/auth/auth.service';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule, TopNavbarComponent, SidebarComponent],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit, OnDestroy {
  // Inject services using the new inject function
  private dashboardDataService = inject(DashboardDataService);
  private authService = inject(AuthService);
  private sidebarService = inject(SidebarService);
  private destroy$ = new Subject<void>();

  // Signal-based reactive data
  dashboard = computed(() => this.dashboardDataService.dashboard());
  widgets = computed(() => this.dashboardDataService.widgets());
  analytics = computed(() => this.dashboardDataService.analytics());
  isLoading = computed(() => this.dashboardDataService.isLoading());
  currentUser = computed(() => this.authService.currentUser());
  isAuthenticated = computed(() => this.authService.isAuthenticated());
  // Computed user data from auth service
  user = computed(() => {
    const currentUser = this.currentUser();
    return currentUser ? {
      name: currentUser.full_name || 'User',
      role: currentUser.role || 'User',
      avatar: currentUser.avatar || 'assets/images/default-avatar.svg'
    } : {
      name: 'Guest',
      role: 'Guest',
      avatar: 'assets/images/default-avatar.svg'
    };
  });

  // Computed stats from analytics
  stats = computed(() => {
    const analyticsData = this.analytics();
    return {
      transactions: 7623, // This would come from transaction service
      turnover: analyticsData.totalSpent || 0,
      mediumBasket: 124, // This would be calculated from transaction data
      customerRetention: 92 // This would come from customer analytics
    };
  });

  // Static data - these could be moved to services later
  locations = [
    { name: '001 - SOUSSE', tickets: 3520 },
    { name: '002 - TUNISIA MALL', tickets: 1620 },
    { name: '003 - SFAX MALL', tickets: 7862 }
  ];

  tableData = [
    { ticketNumber: 10, product: 'Hat', customerId: '#20462', date: '13/05/2022', amount: 16, paymentMode: 'Cash', status: 'Loyal' },
    { ticketNumber: 9, product: 'Laptop', customerId: '#18933', date: '22/05/2022', amount: 2230, paymentMode: 'Cash', status: 'New' }
  ];

  isMobile = window.innerWidth <= 768;
  isMobileMenuOpen = false;

  constructor() {}

  ngOnInit() {
    console.log('Dashboard component initialized');

    // Check authentication status
    if (!this.isAuthenticated()) {
      console.warn('User not authenticated, dashboard data may not be available');
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Method to refresh dashboard data
  refreshDashboard() {
    console.log('Refreshing dashboard data...');
  }

  // Method to handle widget interactions
  onWidgetAction(widgetId: string, action: string) {
    console.log(`Widget ${widgetId} action: ${action}`);
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.isMobile = window.innerWidth <= 768;
  }

  toggleSidebar() {
    this.sidebarService.toggleSidebar();
  }

  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  closeMobileMenu() {
    this.isMobileMenuOpen = false;
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    const navbarRight = document.querySelector('.navbar-right');
    const avatarContainer = document.querySelector('.avatar-container');
    
    if (navbarRight && !navbarRight.contains(target) && !avatarContainer?.contains(target)) {
      this.closeMobileMenu();
    }
  }
}