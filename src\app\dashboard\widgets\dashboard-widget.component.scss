.widget-container {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;

  h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
  }

  .widget-actions {
    display: flex;
    gap: 8px;

    .widget-action-btn {
      background: none;
      border: none;
      padding: 6px;
      border-radius: 6px;
      cursor: pointer;
      color: #666;
      transition: all 0.2s ease;

      &:hover {
        background: #f5f5f5;
        color: #333;
      }

      i {
        font-size: 0.9rem;
      }
    }
  }
}

.widget-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

// Budget Overview Widget Styles
.budget-overview {
  .budget-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .stat-item {
      text-align: center;
      flex: 1;

      .stat-label {
        display: block;
        font-size: 0.85rem;
        color: #666;
        margin-bottom: 5px;
      }

      .stat-value {
        display: block;
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;

        &.spent {
          color: #e74c3c;
        }

        &.remaining {
          color: #27ae60;
        }
      }
    }
  }

  .budget-progress {
    .progress-bar {
      width: 100%;
      height: 8px;
      background: #f0f0f0;
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 8px;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #3498db, #2980b9);
        transition: width 0.3s ease;
      }
    }

    .progress-text {
      font-size: 0.9rem;
      color: #666;
    }
  }
}

// Savings Progress Widget Styles
.savings-progress {
  .savings-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .stat-item {
      text-align: center;
      flex: 1;

      .stat-label {
        display: block;
        font-size: 0.85rem;
        color: #666;
        margin-bottom: 5px;
      }

      .stat-value {
        display: block;
        font-size: 1.1rem;
        font-weight: 600;
        color: #27ae60;
      }
    }
  }

  .savings-progress-bar {
    .progress-bar {
      width: 100%;
      height: 8px;
      background: #f0f0f0;
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 8px;

      .progress-fill.savings {
        height: 100%;
        background: linear-gradient(90deg, #27ae60, #2ecc71);
        transition: width 0.3s ease;
      }
    }

    .progress-text {
      font-size: 0.9rem;
      color: #666;
    }
  }
}

// Loyalty Cards Widget Styles
.loyalty-cards {
  text-align: center;

  .cards-count {
    margin-bottom: 20px;

    i {
      font-size: 2rem;
      color: #3498db;
      margin-bottom: 10px;
      display: block;
    }

    .count {
      display: block;
      font-size: 1.5rem;
      font-weight: 600;
      color: #333;
      margin-bottom: 5px;
    }

    .label {
      font-size: 0.9rem;
      color: #666;
    }
  }

  .cards-preview {
    .card-item {
      display: flex;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .card-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 10px;
      }

      .card-name {
        font-size: 0.9rem;
        color: #333;
      }
    }
  }
}

// Recent Transactions Widget Styles
.recent-transactions {
  .transactions-list {
    .transaction-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .transaction-info {
        flex: 1;

        .transaction-description {
          display: block;
          font-size: 0.9rem;
          color: #333;
          margin-bottom: 2px;
        }

        .transaction-date {
          font-size: 0.8rem;
          color: #666;
        }
      }

      .transaction-amount {
        font-size: 0.9rem;
        font-weight: 600;
        color: #27ae60;

        &.negative {
          color: #e74c3c;
        }
      }
    }
  }
}

// Quick Actions Widget Styles
.quick-actions {
  .action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;

    .action-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 15px 10px;
      background: #f8f9fa;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #e9ecef;
        transform: translateY(-2px);
      }

      i {
        font-size: 1.2rem;
        color: #3498db;
        margin-bottom: 8px;
      }

      span {
        font-size: 0.8rem;
        color: #333;
        text-align: center;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .widget-container {
    padding: 15px;
  }

  .budget-overview .budget-stats,
  .savings-progress .savings-stats {
    flex-direction: column;
    gap: 15px;

    .stat-item {
      text-align: left;
    }
  }

  .quick-actions .action-buttons {
    grid-template-columns: 1fr;
  }
}
