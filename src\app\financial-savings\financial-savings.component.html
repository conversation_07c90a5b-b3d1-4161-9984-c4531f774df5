<app-top-navbar></app-top-navbar>
<app-sidebar></app-sidebar>

<!-- Authentication Warning -->
<app-auth-warning></app-auth-warning>

<div class="savings-container" [class.dark-mode]="isDarkMode" *ngIf="isAuthenticated()">
  <div class="savings-header">
    <h1>Financial Savings</h1>
  </div>

  <!-- Saving Plans List Section -->
  <div class="saving-plans-list" *ngIf="savingPlans.length > 0">
    <h2>Saving plans</h2>
    <div class="saving-plans-card">
      <div class="saving-plans-items">
        <div *ngFor="let plan of savingPlans" class="saving-plan-item" (click)="editSavingPlan(plan.id)">
          <div class="saving-plan-item-header">
            <h3>{{ plan.name }}</h3>
            <div class="saving-plan-item-period">
              <ng-container *ngIf="plan.durationAmount && plan.durationValue && plan.durationType; else showPeriod">
                {{ plan.durationValue }} {{ plan.durationType }}
                <span class="duration-conversion-display" *ngIf="getDurationConversion(plan)">
                  {{ getDurationConversion(plan) }}
                </span>
              </ng-container>
              <ng-template #showPeriod>
                {{ plan.period }}
              </ng-template>
            </div>
          </div>
          <div class="saving-plan-objective">
            <div class="objective-label">Objective</div>
            <div class="objective-amount">{{ plan.objective }} TND</div>
            <div class="current-amount">Current: {{ plan.current }} TND</div>
          </div>
          <!-- Duration Information -->
          <div class="saving-plan-duration" *ngIf="plan.durationAmount && plan.durationValue && plan.durationType">
            <div class="duration-label">Duration Savings</div>
            <div class="duration-details">
              {{ plan.durationAmount }} TND × {{ plan.durationValue }} {{ plan.durationType }}
            </div>
          </div>
          <div class="saving-plan-progress">
            <div class="progress-bar">
              <div class="progress-fill"
                   [style.width.%]="plan.percentComplete || 0"
                   [ngClass]="{
                     'purple-progress': true
                   }"></div>
            </div>
            <div class="last-update">Last update on {{ formatDate(plan.lastUpdate) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State Message -->
  <div class="empty-savings-message" *ngIf="savingPlans.length === 0">
    <div class="message-content">
      <i class="fas fa-piggy-bank"></i>
      <h3>No Saving Plans Yet</h3>
      <p>Create your first saving plan to start tracking your financial goals.</p>
    </div>
  </div>

  <div class="create-savings-container">
    <button class="create-savings-btn" (click)="createNewSavingPlan()">
      <i class="fas fa-plus-circle"></i> Create A New Saving Plan
    </button>
  </div>
</div>
