<app-top-navbar></app-top-navbar>
<app-sidebar></app-sidebar>

<!-- Authentication Warning -->
<app-auth-warning></app-auth-warning>

<div class="new-loyalty-card-container" *ngIf="isAuthenticated()">
  <div class="header">
    <h1 class="loyalty-title">New Loyalty Card</h1>
  </div>

  <div class="card-form">
    <div class="form-container">
      <div class="form-icon">
        <i class="fas fa-credit-card"></i>
      </div>
      <h2 class="form-title">Create New Loyalty Card</h2>
      <p class="form-description">Enter your card details to start managing your loyalty cards.</p>
      
      <div class="form-group">
        <label for="cardName">Card Name</label>
        <div class="card-name-input-wrapper">
          <input
            type="text"
            id="cardName"
            [(ngModel)]="cardName"
            placeholder="Click to see our collaborating brands..."
            autocomplete="off"
            class="card-input"
            (input)="onCardNameInput($event)"
            (focus)="showAllBrands()"
            (blur)="hideAutocomplete()">
          <div class="autocomplete-dropdown" *ngIf="showAutocomplete">
            <div class="brand-search-info" *ngIf="filteredBrands.length === 0">
              No matching brands found
            </div>
            <div 
              class="brand-option" 
              *ngFor="let brand of filteredBrands" 
              (click)="selectBrand(brand)"
              (mousedown)="$event.preventDefault()">
              {{ brand }}
            </div>
          </div>
        </div>
      </div>

    <div class="form-group">
      <label for="cardNumber">Card Number</label>
      <input
        type="text"
        id="cardNumber"
        [(ngModel)]="cardNumber"
        placeholder="Example: 1234123412341234"
        autocomplete="off"
        class="card-input">
    </div>

    <div class="form-group">
      <label>Card Color</label>
      <div class="color-selector">
        <div
          *ngFor="let color of availableColors"
          class="color-option"
          [ngStyle]="{'background-color': color}"
          [ngClass]="{'selected': color === selectedColor}"
          (click)="selectColor(color)">
        </div>
      </div>
    </div>

    <div class="form-group">
      <label>Barcode Image (Optional)</label>
      <div class="barcode-upload">
        <div class="barcode-preview" *ngIf="barcodePreview">
          <img [src]="barcodePreview" alt="Barcode Preview">
        </div>
        <button type="button" class="upload-btn" (click)="triggerBarcodeUpload()">
          <i class="fas fa-upload"></i> Upload Barcode Image
        </button>
        <input
          type="file"
          #barcodeFileInput
          style="display: none;"
          accept="image/*"
          (change)="onBarcodeFileSelected($event)">
      </div>
    </div>

    <button
      class="add-card-btn"
      [disabled]="!isFormValid()"
      [ngClass]="{'disabled': !isFormValid()}"
      (click)="addCard()">
      ADD LOYALTY CARD
    </button>
  </div>
</div>
