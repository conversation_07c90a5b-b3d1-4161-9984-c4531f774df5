import { Component, EventEmitter, Input, OnInit, Output, computed, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BudgetData } from '../interfaces/budget';
import { BudgetDataService } from '../core/budget/budget-data.service';
import { ThemeService } from '../services/theme.service';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { NotificationService } from '../services/notification.service';

@Component({
  selector: 'app-budget-selection-modal',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './budget-selection-modal.component.html',
  styleUrls: ['./budget-selection-modal.component.scss']
})
export class BudgetSelectionModalComponent implements OnInit {
  @Input() totalAmount: number = 0;
  @Output() close = new EventEmitter<void>();
  @Output() selectBudget = new EventEmitter<{ budgetId: string, amount: number }>();

  private budgetDataService = inject(BudgetDataService);
  private themeService = inject(ThemeService);
  private router = inject(Router);
  private notificationService = inject(NotificationService);

  // Use computed signal for reactive data
  budgets = computed(() => this.budgetDataService.budgets());

  selectedBudgetId: string = '';
  isDarkMode: boolean = false;

  ngOnInit(): void {
    // Get dark mode status
    this.themeService.isDarkMode$.subscribe(isDark => {
      this.isDarkMode = isDark;
    });

    // Budget data is now handled automatically by signals
    // Don't select any budget by default - leave it empty
    this.selectedBudgetId = '';
  }

  onSelectBudget(): void {
    if (this.selectedBudgetId) {
      this.selectBudget.emit({
        budgetId: this.selectedBudgetId,
        amount: this.totalAmount
      });
    }
  }

  onClose(): void {
    this.close.emit();
  }

  /**
   * Navigate to the budget page to create a new budget
   * and pass the amount to be added to the new budget
   */
  navigateToBudgetCreation(): void {
    // Close the modal
    this.close.emit();

    // Store the amount in localStorage to be used by the budget component
    localStorage.setItem('pending-budget-amount', this.totalAmount.toString());

    // Navigate to the budget page with query parameters
    this.router.navigate(['/budget'], {
      queryParams: {
        pendingAmount: this.totalAmount,
        autoAdd: true
      }
    });

    // Show a notification to guide the user
    this.notificationService.addNotification({
      title: 'Create a Budget',
      message: 'Create a budget to automatically add your selected tickets',
      read: false,
      time: new Date()
    });
  }
}
