/* Component Scoped Reset */
:host {
  display: block;
  box-sizing: border-box;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  position: relative;
  z-index: 1;

  // Override layout component margin to prevent double margin
  margin-left: 0 !important;

  // Ensure proper positioning on desktop
  @media (min-width: 769px) {
    margin-left: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
  }
}

// Global responsive fixes for mobile
:host * {
  box-sizing: border-box;
  max-width: 100%;
}

// Override layout component margin on mobile
@media (max-width: 768px) {
  :host {
    margin-left: 0 !important;
    width: 100% !important;
  }
}

/* Design Tokens */
:host {
  --primary-color: #A78BFA;
  --accent-color: #6366F1;
  --success-color: #10B981;
  --text-primary: var(--text-color, #111827);
  --text-secondary: var(--text-color, #6B7280);
  --card-background: var(--card-bg, #FFFFFF);
  --background-color: var(--primary-bg, #F9FAFB);
  --shadow-sm: 0 2px 6px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --sidebar-width: 250px;
  --card-min-height: 180px;
  --transition: all 0.2s ease;
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 22px;
  --border-color-light: var(--border-color, #F1F5F9);
  --hover-bg-light: var(--hover-bg, #F3F4F6);
}

/* Dashboard Container */
// Loading and Authentication States
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  color: #666;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #A78BFA;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  p {
    font-size: 1.1rem;
    margin: 0;
  }
}



.dashboard-container {
  font-family: var(--font-family);
  background: var(--background-color);
  min-height: 100vh;
  padding: var(--spacing-xl);
  transition: var(--transition);
  overflow-x: hidden; // Prevent horizontal scroll on mobile
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  position: relative;
  z-index: 1; // Ensure dashboard content is above other elements

  // Account for layout component wrapper on desktop
  @media (min-width: 769px) {
    margin-left: 0; // Reset any inherited margin
    padding-left: var(--spacing-xl);
    width: 100%;
    max-width: 100%;
  }

  /* Main Content Grid */
  .main-content {
    max-width: 100%;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-lg);
    margin-left: var(--sidebar-width);
    padding-right: var(--spacing-xl);
    width: calc(100% - var(--sidebar-width));
    max-width: calc(100vw - var(--sidebar-width));
    overflow-x: hidden;
    position: relative;
    z-index: 2; // Ensure main content is above dashboard container

    /* Base Card Styles */
    .card {
      background: var(--card-background);
      border-radius: var(--border-radius-lg);
      box-shadow: var(--shadow-sm);
      padding: var(--spacing-md);
      transition: var(--transition);
      min-height: var(--card-min-height);
      overflow: hidden; // Prevent content from breaking out of cards
      width: 100%;
      max-width: 100%;

      &:hover {
        box-shadow: var(--shadow-md);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-sm);

        h2 {
          font-size: var(--font-size-base);
          font-weight: 600;
          color: var(--text-primary);
          line-height: 1.3;
        }

        .menu-dots {
          font-size: var(--font-size-lg);
          color: var(--text-secondary);
          cursor: pointer;
          padding: var(--spacing-xs);
          border-radius: var(--border-radius-sm);
          transition: var(--transition);

          &:hover {
            background: var(--hover-bg-light);
          }
        }
      }

      /* View All Button */
      .view-all {
        background: var(--secondary-bg, #F3F4FF);
        color: var(--accent-color);
        border: none;
        border-radius: var(--border-radius-md);
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
        font-weight: 500;
        cursor: pointer;
        transition: var(--transition);
        position: relative;

        &:hover:not(:disabled) {
          background: var(--hover-bg, #E8E8FF);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          transform: translateY(-1px);
        }

        &:disabled {
          background: #f0f0f0;
          color: #999;
          cursor: not-allowed;
          opacity: 0.6;
        }

        // Add a notification dot when there are transactions
        &:not(:disabled)::after {
          content: '';
          position: absolute;
          top: -2px;
          right: -2px;
          width: 8px;
          height: 8px;
          background: #ff4757;
          border-radius: 50%;
          animation: pulse 2s infinite;
        }
      }
    }

    /* Lifetime Expenses Card */
    .lifetime-expenses {
      grid-column: 1 / 3;
      grid-row: 1;
      min-height: var(--card-min-height);
      width: 100%;

      .card-header h2 {
        font-size: var(--font-size-xl);
      }

      .amount {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin: var(--spacing-sm) 0;
        line-height: 1.2;
      }

      .meta-info {
        display: flex;
        justify-content: space-between;
        margin-top: var(--spacing-md);
        padding-top: var(--spacing-sm);
        border-top: 1px solid var(--border-color-light);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        font-weight: 500;

        .articles,
        .time {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
        }
      }
    }

    /* Current Month Expenses Card */
    .current-month {
      grid-column: 1 / 3;
      grid-row: 2;
      min-height: var(--card-min-height);
      width: 100%;

      .card-header h2 {
        font-size: var(--font-size-xl);
      }

      .amount {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--primary-color);
        margin: var(--spacing-sm) 0;
        line-height: 1.2;
      }

      .meta-info {
        display: flex;
        justify-content: space-between;
        margin-top: var(--spacing-md);
        padding-top: var(--spacing-sm);
        border-top: 1px solid var(--border-color-light);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        font-weight: 500;

        .articles,
        .time {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
        }
      }
    }

    /* Expenses Category Card */
    .expenses-category {
      grid-column: 3 / 5;
      grid-row: 1 / 3;
      width: 100%;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-md);

      .card-header {
        grid-column: 1 / -1;

        .title-wrapper {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);

          .subtitle {
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
            font-weight: 400;
          }
        }

        .dropdown-selector {
          background: var(--secondary-bg, #F9FAFB);
          border: 1px solid var(--border-color, #E5E7EB);
          border-radius: var(--border-radius-sm);
          padding: var(--spacing-xs) var(--spacing-md);
          font-size: var(--font-size-sm);
          font-weight: 500;
          color: var(--text-primary);
          cursor: pointer;
          transition: var(--transition);

          &:hover {
            background: var(--hover-bg-light);
          }

          &:after {
            content: '▼';
            font-size: var(--font-size-xs);
            margin-left: var(--spacing-sm);
            color: var(--text-secondary);
          }
        }
      }

      .chart-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 300px;
        position: relative;
        grid-column: 1;
        aspect-ratio: 1;
        overflow: hidden;
        margin: 0 auto; // Center the chart container

        canvas {
          max-height: 250px;
          max-width: 250px;
          width: 100% !important;
          height: 100% !important;
          aspect-ratio: 1;
          object-fit: contain;
        }

        // Mobile responsive adjustments
        @media (max-width: 768px) {
          height: 250px;

          canvas {
            max-height: 200px;
            max-width: 200px;
          }
        }

        @media (max-width: 480px) {
          height: 200px;

          canvas {
            max-height: 150px;
            max-width: 150px;
          }
        }
      }

      .chart-legend {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) 0;
        font-size: var(--font-size-sm);
        font-weight: 500;
        grid-column: 2;
        align-self: center;
        justify-self: start;
        max-height: 300px;
        overflow-y: auto;

        .legend-item {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);

          .color-indicator {
            width: 12px;
            height: 12px;
            border-radius: var(--border-radius-sm);
            border: 1px solid rgba(0, 0, 0, 0.1);

            &.food {
              background: #A78BFA;
            }

            &.clothes {
              background: #F9A8D4;
            }

            &.leisure {
              background: #60A5FA;
            }

            &.medicines {
              background: #FBBF24;
            }
          }
        }
      }
    }

    /* Expenses Per Month Card */
    .expenses-month {
      grid-column: 1 / 3;
      grid-row: 3;
      width: 100%;

      .card-header {
        margin-bottom: var(--spacing-lg);
      }

      .amount {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--accent-color);
        margin-bottom: var(--spacing-sm);
      }

      .chart-container {
        height: 220px;
        width: 100%;
        position: relative;
        display: flex;
        justify-content: center; // Center the chart for better space utilization
        align-items: center;
        margin-bottom: var(--spacing-md);
        overflow: hidden; // Prevent chart from going outside card borders

        canvas {
          position: absolute;
          top: 0;
          left: 0;
          width: 100% !important;
          height: 100% !important;
          max-width: 100%;
          max-height: 100%;
        }

        // Dynamic width handling for few months
        &.narrow-chart {
          max-width: 300px;
          margin: 0 0 var(--spacing-md) 0; // Remove auto margin to prevent centering, keep bottom margin
          justify-content: flex-start; // Ensure left alignment

          canvas {
            position: relative;
            width: auto !important;
            height: auto !important;
            max-width: 100%;
            max-height: 100%;
          }

          // Mobile adjustments for narrow chart
          @media (max-width: 768px) {
            max-width: 250px;
          }

          @media (max-width: 480px) {
            max-width: 200px;
          }
        }

        // Mouse wheel scroll functionality
        cursor: grab;

        &:active {
          cursor: grabbing;
        }

        &.scrollable-chart {
          position: relative;
          overflow-x: auto; // Allow horizontal scrolling
          overflow-y: hidden;

          // Add visual indicator that scrolling is available
          &::after {
            content: "Scroll or swipe to navigate months";
            position: absolute;
            bottom: 5px;
            right: 10px;
            background: rgba(107, 72, 255, 0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 9px;
            font-weight: 500;
            opacity: 0.7;
            pointer-events: none;
            z-index: 10;
          }

          canvas {
            min-width: 100%;
          }
        }

        // Mobile responsive adjustments
        @media (max-width: 768px) {
          height: 180px;
          max-width: 100% !important;
          width: 100% !important;
        }

        @media (max-width: 480px) {
          height: 160px;
          max-width: 100% !important;
          width: 100% !important;
        }
      }



      .monthly-summary {
        margin-top: var(--spacing-md);
        padding-top: var(--spacing-sm);
        border-top: 1px solid var(--border-color-light);
        display: flex;
        justify-content: space-between;
        font-size: var(--font-size-sm);
        color: var(--text-secondary);

        .summary-item {
          font-weight: 500;
        }
      }

      .test-controls {
        margin-top: var(--spacing-md);
        padding: var(--spacing-sm);
        background: rgba(107, 72, 255, 0.05);
        border-radius: 8px;
        border: 1px solid rgba(107, 72, 255, 0.1);

        button {
          padding: 6px 12px;
          font-size: 12px;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.2s ease;

          &:first-of-type {
            background: #28a745;
            color: white;
            margin-right: 8px;

            &:hover {
              background: #218838;
              transform: translateY(-1px);
            }
          }

          &:last-of-type {
            background: #dc3545;
            color: white;

            &:hover {
              background: #c82333;
              transform: translateY(-1px);
            }
          }
        }
      }
    }

    /* Saving Plan Card */
    .saving-plan {
      grid-column: 3 / 5;
      grid-row: 3;
      width: 100%;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-md);

      .card-header {
        grid-column: 1 / -1;
      }

      .chart-container {
        height: 350px;
        position: relative;
        overflow: hidden;

        canvas {
          max-height: 320px;
          max-width: 320px;
          width: 100% !important;
          height: 100% !important;
          aspect-ratio: 1;
          object-fit: contain;
        }

        // Mobile responsive adjustments
        @media (max-width: 768px) {
          height: 280px;

          canvas {
            max-height: 240px;
            max-width: 240px;
          }
        }

        @media (max-width: 480px) {
          height: 200px;

          canvas {
            max-height: 170px;
            max-width: 170px;
          }
        }

        .chart-center {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;

          .saving-label {
            font-size: var(--font-size-sm);
            font-weight: 600;
            color: var(--text-primary);
          }

          .saving-sublabel {
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
          }
        }
      }

      .chart-legend {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
        padding: var(--spacing-md) 0;
        font-size: var(--font-size-sm);
        font-weight: 500;
        grid-column: 2;
        align-self: center;
        justify-self: start;
        margin-top: calc(var(--spacing-xl) * 2);
        max-height: 300px;
        overflow-y: auto;

        .legend-item {
          display: flex;
          align-items: flex-start;
          gap: var(--spacing-md);
          margin-bottom: var(--spacing-md);

          .color-indicator {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 1px solid rgba(0, 0, 0, 0.1);
            flex-shrink: 0;
            margin-top: 4px;
          }

          .legend-content {
            display: flex;
            flex-direction: column;
            gap: 6px;
            flex: 1;

            .plan-name {
              font-size: var(--font-size-md);
              color: var(--text-primary);
              font-weight: 700;
              line-height: 1.4;
            }

            .plan-details {
              font-size: 14px;
              color: var(--text-secondary);
              line-height: 1.4;
              opacity: 1;
              font-weight: 500;
            }
          }

          span {
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
            line-height: 1.3;
          }
        }

        .no-plans {
          text-align: center;
          color: var(--text-secondary);
          font-style: italic;
          padding: var(--spacing-md);
        }
      }

      .saving-summary {
        grid-column: 1 / -1;
        margin-top: var(--spacing-md);
        padding-top: var(--spacing-sm);
        border-top: 1px solid var(--border-color-light);

        .summary-row {
          display: flex;
          justify-content: space-between;
          font-size: var(--font-size-sm);
          color: var(--text-secondary);
          font-weight: 500;

          span {
            &:first-child {
              color: var(--success-color);
            }
            &:last-child {
              color: var(--accent-color);
            }
          }
        }
      }

      // Dark mode support for saving plan
      :host-context([data-theme="dark"]) & {
        .chart-legend {
          .legend-item {
            .legend-content {
              .plan-name {
                color: #ffffff;
                font-size: var(--font-size-md);
                font-weight: 700;
              }

              .plan-details {
                color: #48BB78;
                font-size: 14px;
                opacity: 1;
                font-weight: 500;
              }
            }
          }

          .no-plans {
            color: #aaaaaa;
          }
        }
      }
    }

    /* Transactions Card */
    .transactions {
      grid-column: 1 / -1;
      grid-row: 4;
      width: 100%;

      .transaction-list {
        margin-top: var(--spacing-sm);

        .transaction-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--spacing-sm) 0;
          border-bottom: 1px solid var(--border-color-light);
          transition: var(--transition);

          &:last-child {
            border-bottom: none;
          }

          .transaction-left {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);

            .indicator {
              width: 8px;
              height: 8px;
              background: var(--primary-color);
              border-radius: 2px;
            }

            .transaction-date {
              font-size: var(--font-size-sm);
              color: var(--text-secondary);
              font-weight: 400;
            }
          }

          .amount {
            font-size: var(--font-size-sm);
            font-weight: 600;
            color: var(--success-color);
          }
        }
      }
    }
  }
}

// Transactions Popup Modal Styles
.transactions-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;

  // Dark mode overlay
  :host-context([data-theme="dark"]) & {
    background: rgba(0, 0, 0, 0.7);
  }
}

.transactions-popup {
  background: var(--card-bg, #ffffff);
  border-radius: 16px;
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease-out;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color, #e9ecef);

  // Dark mode popup
  :host-context([data-theme="dark"]) & {
    background: var(--card-bg, #2d2d2d);
    border-color: var(--border-color, #404040);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
  }
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid var(--border-color, #e9ecef);
  background: var(--card-bg, #ffffff);
  color: var(--text-color, #333);
  position: relative;

  // Add subtle gradient accent
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color, #A78BFA), var(--accent-color, #6366F1));
    border-radius: 16px 16px 0 0;
  }

  h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color, #333);
  }

  .close-btn {
    background: var(--secondary-bg, #f8f9fa);
    border: 1px solid var(--border-color, #e9ecef);
    color: var(--text-color, #666);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: var(--hover-bg, #e9ecef);
      color: var(--text-color, #333);
      transform: scale(1.05);
    }
  }

  // Dark mode header
  :host-context([data-theme="dark"]) & {
    background: var(--card-bg, #2d2d2d);
    border-bottom-color: var(--border-color, #404040);

    h2 {
      color: var(--text-color, #ffffff);
    }

    .close-btn {
      background: var(--secondary-bg, #404040);
      border-color: var(--border-color, #555555);
      color: var(--text-color, #ffffff);

      &:hover {
        background: var(--hover-bg, #555555);
        color: var(--text-color, #ffffff);
      }
    }
  }
}

.popup-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.transactions-summary {
  display: flex;
  gap: 32px;
  padding: 24px 32px;
  background: var(--secondary-bg, #f8f9fa);
  border-bottom: 1px solid var(--border-color, #e9ecef);

  .summary-item {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .label {
      font-size: 0.9rem;
      color: var(--text-secondary, #666);
      font-weight: 500;
    }

    .value {
      font-size: 1.2rem;
      font-weight: 600;
      color: var(--text-color, #333);
    }
  }

  // Dark mode summary
  :host-context([data-theme="dark"]) & {
    background: var(--secondary-bg, #333333);
    border-bottom-color: var(--border-color, #404040);

    .summary-item {
      .label {
        color: var(--text-secondary, #aaaaaa);
      }

      .value {
        color: var(--text-color, #ffffff);
      }
    }
  }
}

.transactions-table-container {
  flex: 1;
  overflow: auto;
  padding: 0 32px 32px;
}

.transactions-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 16px;

  th {
    background: var(--secondary-bg, #f8f9fa);
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: var(--text-color, #333);
    border-bottom: 2px solid var(--border-color, #e9ecef);
    position: sticky;
    top: 0;
    z-index: 10;
  }

  td {
    padding: 16px;
    border-bottom: 1px solid var(--border-color, #f0f0f0);
    vertical-align: middle;
    color: var(--text-color, #333);
  }

  .transaction-row {
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--hover-bg, #f8f9fa);
    }
  }

  // Dark mode table
  :host-context([data-theme="dark"]) & {
    th {
      background: var(--secondary-bg, #333333);
      color: var(--text-color, #ffffff);
      border-bottom-color: var(--border-color, #404040);
    }

    td {
      color: var(--text-color, #ffffff);
      border-bottom-color: var(--border-color, #404040);
    }

    .transaction-row:hover {
      background: var(--hover-bg, #404040);
    }
  }

  .ticket-number {
    font-weight: 600;
    color: var(--primary-color, #007bff);
  }

  .product-info {
    .product-details {
      display: flex;
      align-items: center;
      gap: 12px;

      .product-image {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        object-fit: cover;
        border: 1px solid var(--border-color, #e9ecef);
      }

      .product-text {
        display: flex;
        flex-direction: column;
        gap: 2px;

        .product-name {
          font-weight: 500;
          color: var(--text-color, #333);
          font-size: 0.9rem;
        }

        .brand-name {
          font-size: 0.8rem;
          color: var(--text-secondary, #666);
        }
      }
    }
  }

  .date {
    color: var(--text-secondary, #666);
    font-size: 0.9rem;
  }

  .amount {
    .amount-value {
      font-weight: 600;
      color: var(--success-color, #28a745);
      font-size: 0.95rem;
    }
  }

  .payment-mode {
    color: var(--text-secondary, #666);
    font-size: 0.9rem;
  }

  // Dark mode cell styles
  :host-context([data-theme="dark"]) & {
    .ticket-number {
      color: var(--primary-color, #A78BFA);
    }

    .product-info .product-details {
      .product-image {
        border-color: var(--border-color, #404040);
      }

      .product-text {
        .product-name {
          color: var(--text-color, #ffffff);
        }

        .brand-name {
          color: var(--text-secondary, #aaaaaa);
        }
      }
    }

    .date {
      color: var(--text-secondary, #aaaaaa);
    }

    .amount .amount-value {
      color: var(--success-color, #10B981);
    }

    .payment-mode {
      color: var(--text-secondary, #aaaaaa);
    }
  }

  .status {
    .status-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 500;
      text-transform: uppercase;

      &.status-completed {
        background: #d4edda;
        color: #155724;
      }

      &.status-pending {
        background: #fff3cd;
        color: #856404;
      }

      &.status-cancelled {
        background: #f8d7da;
        color: #721c24;
      }

      &.status-failed {
        background: #f5c6cb;
        color: #721c24;
      }
    }

    // Dark mode status badges
    :host-context([data-theme="dark"]) & {
      .status-badge {
        &.status-completed {
          background: rgba(16, 185, 129, 0.2);
          color: #10B981;
        }

        &.status-pending {
          background: rgba(245, 158, 11, 0.2);
          color: #F59E0B;
        }

        &.status-cancelled {
          background: rgba(239, 68, 68, 0.2);
          color: #EF4444;
        }

        &.status-failed {
          background: rgba(239, 68, 68, 0.2);
          color: #EF4444;
        }
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary, #666);

  i {
    font-size: 3rem;
    margin-bottom: 16px;
    color: var(--border-color, #ccc);
  }

  h3 {
    margin: 0 0 8px 0;
    font-size: 1.2rem;
    color: var(--text-color, #333);
  }

  p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-secondary, #666);
  }

  // Dark mode empty state
  :host-context([data-theme="dark"]) & {
    color: var(--text-secondary, #aaaaaa);

    i {
      color: var(--border-color, #555555);
    }

    h3 {
      color: var(--text-color, #ffffff);
    }

    p {
      color: var(--text-secondary, #aaaaaa);
    }
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// Responsive Design for Popup
@media (max-width: 768px) {
  .transactions-popup {
    width: 95%;
    max-height: 95vh;
  }

  .popup-header {
    padding: 16px 20px;

    h2 {
      font-size: 1.2rem;
    }
  }

  .transactions-summary {
    flex-direction: column;
    gap: 16px;
    padding: 16px 20px;
  }

  .transactions-table-container {
    padding: 0 20px 20px;
  }

  .transactions-table {
    font-size: 0.8rem;

    th,
    td {
      padding: 8px 4px;
    }

    .product-info .product-details {
      gap: 8px;

      .product-image {
        width: 32px;
        height: 32px;
      }
    }
  }
}

@media (max-width: 480px) {
  .transactions-popup {
    width: 98%;
    max-height: 98vh;
  }

  .transactions-table {
    th:nth-child(3),
    td:nth-child(3),
    th:nth-child(5),
    td:nth-child(5) {
      display: none; // Hide date and payment mode columns on very small screens
    }
  }
}

/* Desktop-specific styles for proper layout */
@media (min-width: 1025px) {
  .dashboard-container {
    margin-left: 0; // Remove any inherited margin
    padding-left: var(--spacing-xl);

    .main-content {
      margin-left: var(--sidebar-width);
      width: calc(100% - var(--sidebar-width));
      max-width: calc(100vw - var(--sidebar-width));
      padding-right: var(--spacing-xl);
      position: relative;
      z-index: 2;

      // Ensure proper grid layout on desktop
      grid-template-columns: repeat(4, 1fr);
      grid-template-rows: auto;

      // Ensure cards are properly positioned
      .card {
        position: relative;
        z-index: 3;
        width: 100%;
        max-width: 100%;
      }

      // Specific fixes for right side cards
      .expenses-category {
        grid-column: 3 / 5;
        grid-row: 1 / 3;
        position: relative;
        z-index: 4;
        width: 100%;
        max-width: 100%;
      }

      .saving-plan {
        grid-column: 3 / 5;
        grid-row: 3;
        position: relative;
        z-index: 4;
        width: 100%;
        max-width: 100%;
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-container {
    .main-content {
      grid-template-columns: repeat(2, 1fr);
      margin-left: var(--sidebar-width);
      width: calc(100% - var(--sidebar-width));
      max-width: calc(100vw - var(--sidebar-width));
      padding-right: var(--spacing-lg);

      .lifetime-expenses {
        grid-column: 1;
        grid-row: 1;
      }

      .current-month {
        grid-column: 2;
        grid-row: 1;
      }

      .expenses-category {
        grid-column: 1 / -1;
        grid-row: 2;
      }

      .expenses-month {
        grid-column: 1 / -1;
        grid-row: 3;
      }

      .saving-plan {
        grid-column: 1 / -1;
        grid-row: 4;
      }

      .transactions {
        grid-column: 1 / -1;
        grid-row: 5;
      }
    }
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: var(--spacing-sm);
    margin-left: 0 !important;

    .main-content {
      margin-left: 0 !important;
      padding-right: var(--spacing-sm) !important;
      grid-template-columns: 1fr !important;
      gap: var(--spacing-md);
      width: 100%;
      max-width: 100%;

      .card {
        padding: var(--spacing-md);
        width: 100%;
        max-width: 100%;
        min-width: 0; // Allow cards to shrink
        grid-column: 1 !important;
      }

      .lifetime-expenses {
        grid-row: 1;
      }

      .current-month {
        grid-row: 2;
      }

      .expenses-category {
        grid-row: 3;
        grid-template-columns: 1fr !important;

        .chart-container {
          grid-column: 1;
          height: 250px;
        }

        .chart-legend {
          grid-column: 1;
          flex-direction: row;
          flex-wrap: wrap;
          justify-content: center;
          padding: var(--spacing-md) 0;
        }
      }

      .expenses-month {
        grid-row: 4;
      }

      .saving-plan {
        grid-row: 5;
        grid-template-columns: 1fr !important;

        .chart-container {
          grid-column: 1;
          height: 300px;
        }

        .chart-legend {
          grid-column: 1;
          flex-direction: row;
          flex-wrap: wrap;
          justify-content: center;
          padding: var(--spacing-md) 0;
          margin-top: var(--spacing-lg);
        }
      }

      .transactions {
        grid-row: 6;
      }
    }
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: var(--spacing-xs);
    margin-left: 0 !important;

    .main-content {
      margin-left: 0 !important;
      padding-right: var(--spacing-xs) !important;
      gap: var(--spacing-sm);
      width: 100%;

      .card {
        padding: var(--spacing-sm);
        min-height: auto;
        width: 100%;
        max-width: 100%;

        .card-header h2 {
          font-size: var(--font-size-sm);
          line-height: 1.2;
        }
      }

      .amount {
        font-size: 1.3rem;
      }

      .meta-info {
        flex-direction: column;
        gap: var(--spacing-xs);
        text-align: left;
      }

      .expenses-category {
        .chart-container {
          height: 180px;
        }

        .chart-legend {
          flex-direction: column;
          gap: var(--spacing-xs);
          text-align: left;
        }
      }

      .saving-plan {
        .chart-container {
          height: 240px;
        }

        .chart-legend {
          flex-direction: column;
          gap: var(--spacing-xs);
          text-align: left;
        }
      }

      .expenses-month {
        .chart-container {
          height: 140px;
        }
      }
    }
  }
}