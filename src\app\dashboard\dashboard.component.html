<!-- Navigation Components -->
<app-top-navbar></app-top-navbar>
<app-sidebar></app-sidebar>

<div class="dashboard-container">
  <!-- Loading State -->
  <div *ngIf="isLoading()" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Loading dashboard...</p>
  </div>

  <!-- Authentication Warning -->
  <div *ngIf="!isAuthenticated()" class="auth-warning">
    <div class="warning-card">
      <i class="fas fa-exclamation-triangle"></i>
      <h3>Authentication Required</h3>
      <p>Please log in to view your personalized dashboard.</p>
      <button routerLink="/login" class="login-btn">Go to Login</button>
    </div>
  </div>

  <!-- Main Dashboard Content -->
  <div *ngIf="isAuthenticated() && !isLoading()" class="main-content">
    <!-- Locations -->
    <div class="locations">
      <div class="location-card" *ngFor="let location of locations">
        <div class="location-icon">
          <i class="fas fa-store"></i>
        </div>
        <div class="location-details">
          <p class="location-name">{{ location.name }}</p>
          <p class="location-tickets">{{ location.tickets }} Tickets</p>
        </div>
      </div>
      <div class="add-location">
        <button class="add-btn">
          <i class="fas fa-plus"></i> Add Location
        </button>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-indicator purple"></div>
        <h3>{{ stats().transactions }}</h3>
        <p>Transactions</p>
        <button class="more-options">
          <i class="fas fa-ellipsis-h"></i>
        </button>
      </div>
      <div class="stat-card">
        <div class="stat-indicator blue"></div>
        <h3>{{ stats().turnover }} TND</h3>
        <p>Turnover</p>
        <button class="more-options">
          <i class="fas fa-ellipsis-h"></i>
        </button>
      </div>
      <div class="stat-card">
        <div class="stat-indicator red"></div>
        <h3>{{ stats().mediumBasket }} TND</h3>
        <p>Medium Basket</p>
        <button class="more-options">
          <i class="fas fa-ellipsis-h"></i>
        </button>
      </div>
      <div class="stat-card">
        <div class="stat-indicator green"></div>
        <h3>{{ stats().customerRetention }}%</h3>
        <p>Customer Return Rate</p>
        <button class="more-options">
          <i class="fas fa-ellipsis-h"></i>
        </button>
      </div>
    </div>

    <!-- Charts -->
    <div class="charts">
      <div class="chart avg-spend">
        <div class="chart-header">
          <div class="chart-title">
            <h3>Statistics</h3>
            <p>Avg Spend By Age Range</p>
          </div>
          <div class="chart-legend">
            <div class="legend-item">
              <span class="legend-dot purple"></span>
              <span>14-25</span>
            </div>
            <div class="legend-item">
              <span class="legend-dot blue"></span>
              <span>26-35</span>
            </div>
            <div class="legend-item">
              <span class="legend-dot green"></span>
              <span>36-50</span>
            </div>
            <div class="legend-item">
              <span class="legend-dot orange"></span>
              <span>51+</span>
            </div>
            <div class="period-selector">
              <span>Last 6 months</span>
              <i class="fas fa-chevron-down"></i>
            </div>
          </div>
        </div>
        <div class="chart-content">
          <!-- This would be replaced with actual chart implementation -->
          <div class="bar-chart">
            <div class="y-axis">
              <div class="y-label">1M</div>
              <div class="y-label">500k</div>
              <div class="y-label">200k</div>
              <div class="y-label">100k</div>
              <div class="y-label">50k</div>
              <div class="y-label">0</div>
            </div>
            <div class="chart-bars">
              <div class="month-column">
                <div class="bar-group">
                  <div class="bar purple" style="height: 15%"></div>
                  <div class="bar blue" style="height: 25%"></div>
                  <div class="bar green" style="height: 10%"></div>
                </div>
                <div class="x-label">JAN</div>
              </div>
              <div class="month-column">
                <div class="bar-group">
                  <div class="bar purple" style="height: 20%"></div>
                  <div class="bar blue" style="height: 35%"></div>
                  <div class="bar green" style="height: 15%"></div>
                </div>
                <div class="x-label">FEB</div>
              </div>
              <div class="month-column">
                <div class="bar-group">
                  <div class="bar purple" style="height: 30%"></div>
                  <div class="bar blue" style="height: 40%"></div>
                  <div class="bar green" style="height: 25%"></div>
                </div>
                <div class="x-label">MAR</div>
              </div>
              <div class="month-column">
                <div class="bar-group">
                  <div class="bar purple" style="height: 40%"></div>
                  <div class="bar blue" style="height: 50%"></div>
                  <div class="bar green" style="height: 30%"></div>
                </div>
                <div class="x-label">APR</div>
              </div>
              <div class="month-column">
                <div class="bar-group">
                  <div class="bar purple" style="height: 45%"></div>
                  <div class="bar blue" style="height: 55%"></div>
                  <div class="bar green" style="height: 35%"></div>
                </div>
                <div class="x-label">MAY</div>
              </div>
              <div class="month-column">
                <div class="bar-group">
                  <div class="bar purple" style="height: 35%"></div>
                  <div class="bar blue" style="height: 45%"></div>
                  <div class="bar green" style="height: 25%"></div>
                </div>
                <div class="x-label">JUN</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="chart monthly-income">
        <div class="chart-header">
          <h3>Statist</h3>
          <p>Monthly income</p>
        </div>
        <div class="income-circle">
          <div class="circle-text">
            <p>Total</p>
            <h4>170,632 TND</h4>
          </div>
        </div>
        <div class="income-legend">
          <div class="legend-item">
            <span class="legend-color turn-over"></span>
            <span>Turnover</span>
            <span class="percentage">65%</span>
          </div>
          <div class="legend-item">
            <span class="legend-color until-objective"></span>
            <span>Until Objective</span>
            <span class="percentage">35%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Table -->
    <div class="table-section">
      <div class="table-header">
        <div class="entries">
          <span>Show</span>
          <select>
            <option>10</option>
          </select>
          <span>entries</span>
        </div>
        <div class="search-container">
          <i class="fas fa-search"></i>
          <input type="text" placeholder="Search..." class="search-input">
        </div>
        <button class="table-options">
          <i class="fas fa-ellipsis-h"></i>
        </button>
      </div>
      <table>
        <thead>
          <tr>
            <th>Ticket Number <i class="fas fa-sort-down"></i></th>
            <th>Product <i class="fas fa-sort-down"></i></th>
            <th>Customer ID <i class="fas fa-sort-down"></i></th>
            <th>Date <i class="fas fa-sort-down"></i></th>
            <th>Amount <i class="fas fa-sort-down"></i></th>
            <th>Payment Mode</th>
            <th>Status</th>
            <th>Action <i class="fas fa-sort-down"></i></th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let row of tableData">
            <td>{{ row.ticketNumber }}</td>
            <td class="product-cell">
              <img [src]="user().avatar" alt="Product" class="product-img">
              <span>{{ row.product }}</span>
            </td>
            <td>{{ row.customerId }}</td>
            <td>{{ row.date }}</td>
            <td>{{ row.amount }} TND</td>
            <td>{{ row.paymentMode }}</td>
            <td>
              <span [ngClass]="row.status === 'Loyal' ? 'status-loyal' : 'status-new'">{{ row.status }}</span>
            </td>
            <td class="action-cell">
              <button class="action-btn view">
                <i class="fas fa-eye"></i>
              </button>
              <button class="action-btn delete">
                <i class="fas fa-trash"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>