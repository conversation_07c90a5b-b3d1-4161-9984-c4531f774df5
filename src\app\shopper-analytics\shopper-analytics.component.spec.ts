import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ShopperAnalyticsComponent } from './shopper-analytics.component';
import { ExpenseService } from '../../services/expense.service';
import { of } from 'rxjs';
import { ProgressCircleComponent } from '../progress-circle/progress-circle.component';
import { TrendChartComponent } from '../trend-chart/trend-chart.component';
import { ExpenseListComponent } from '../expense-list/expense-list.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('ShopperAnalyticsComponent', () => {
  let component: ShopperAnalyticsComponent;
  let fixture: ComponentFixture<ShopperAnalyticsComponent>;
  let expenseServiceSpy: jasmine.SpyObj<ExpenseService>;

  const mockAnalyticsData = {
    budgetProgress: {
      savingsGoalPercentage: 32,
      budgetSpentPercentage: 78
    },
    monthlySpending: {
      amount: 890,
      percentageChange: 1.5,
      trendData: [75, 82, 78, 80, 85, 88, 90]
    },
    expensesByCategory: [
      { id: 1, name: 'Food', amount: 4506, percentageChange: 1.5 },
      { id: 2, name: 'Leisure', amount: 670, percentageChange: 1.5 }
    ],
    expensesByProduct: [
      { id: 1, name: 'Vegetables', amount: 2656, percentageChange: 1.5 },
      { id: 2, name: 'Fruits', amount: 659, percentageChange: 1.5 }
    ],
    summaryValue: '370 + 659'
  };

  beforeEach(async () => {
    expenseServiceSpy = jasmine.createSpyObj('ExpenseService', ['getAnalyticsData']);
    expenseServiceSpy.getAnalyticsData.and.returnValue(of(mockAnalyticsData));

    await TestBed.configureTestingModule({
      imports: [
        ShopperAnalyticsComponent,
        ProgressCircleComponent,
        TrendChartComponent,
        ExpenseListComponent
      ],
      providers: [
        { provide: ExpenseService, useValue: expenseServiceSpy }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(ShopperAnalyticsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load analytics data on init', () => {
    expect(expenseServiceSpy.getAnalyticsData).toHaveBeenCalled();
    expect(component.analyticsData).toEqual(mockAnalyticsData);
    expect(component.loading).toBeFalse();
  });

  it('should display the progress circles', () => {
    const progressCircles = fixture.nativeElement.querySelectorAll('app-progress-circle');
    expect(progressCircles.length).toBe(2);
  });

  it('should display the monthly spending amount', () => {
    const spendingAmount = fixture.nativeElement.querySelector('.spending-amount');
    expect(spendingAmount.textContent.trim()).toContain('890 TND');
  });

  it('should display the trend chart', () => {
    const trendChart = fixture.nativeElement.querySelector('app-trend-chart');
    expect(trendChart).toBeTruthy();
  });

  it('should display the expense lists', () => {
    const expenseLists = fixture.nativeElement.querySelectorAll('app-expense-list');
    expect(expenseLists.length).toBe(2);
  });

  it('should display the summary value', () => {
    const summaryValue = fixture.nativeElement.querySelector('.summary-value');
    expect(summaryValue.textContent.trim()).toBe('370 + 659');
  });
});