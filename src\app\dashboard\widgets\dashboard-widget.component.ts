import { Component, Input, Output, EventEmitter, computed, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DashboardWidget, DashboardWidgetType } from '../../interfaces/dashboard';
import { DashboardDataService } from '../../core/dashboard/dashboard-data.service';
import { BudgetDataService } from '../../core/budget/budget-data.service';
import { FinancialSavingsDataService } from '../../core/financial-savings/financial-savings-data.service';
import { LoyaltyCardDataService } from '../../core/loyalty-cards/loyalty-card-data.service';

@Component({
  selector: 'app-dashboard-widget',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="widget-container" [class]="'widget-' + widget.type">
      <div class="widget-header" *ngIf="widget.config.showHeader">
        <h3>{{ widget.title }}</h3>
        <div class="widget-actions">
          <button class="widget-action-btn" (click)="onRefresh()" title="Refresh">
            <i class="fas fa-sync-alt"></i>
          </button>
          <button class="widget-action-btn" (click)="onSettings()" title="Settings">
            <i class="fas fa-cog"></i>
          </button>
          <button class="widget-action-btn" (click)="onRemove()" title="Remove">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
      
      <div class="widget-content">
        <!-- Budget Overview Widget -->
        <div *ngIf="widget.type === widgetTypes.BudgetOverview" class="budget-overview">
          <div class="budget-stats">
            <div class="stat-item">
              <span class="stat-label">Total Budget</span>
              <span class="stat-value">{{ budgetData().totalBudget | currency:'TND':'symbol':'1.2-2' }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Spent</span>
              <span class="stat-value spent">{{ budgetData().totalSpent | currency:'TND':'symbol':'1.2-2' }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Remaining</span>
              <span class="stat-value remaining">{{ budgetData().totalRemaining | currency:'TND':'symbol':'1.2-2' }}</span>
            </div>
          </div>
          <div class="budget-progress">
            <div class="progress-bar">
              <div class="progress-fill" [style.width.%]="budgetData().percentSpent"></div>
            </div>
            <span class="progress-text">{{ budgetData().percentSpent }}% spent</span>
          </div>
        </div>

        <!-- Savings Progress Widget -->
        <div *ngIf="widget.type === widgetTypes.SavingsProgress" class="savings-progress">
          <div class="savings-stats">
            <div class="stat-item">
              <span class="stat-label">Total Saved</span>
              <span class="stat-value">{{ savingsData().totalCurrent | currency:'TND':'symbol':'1.2-2' }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Target</span>
              <span class="stat-value">{{ savingsData().totalObjective | currency:'TND':'symbol':'1.2-2' }}</span>
            </div>
          </div>
          <div class="savings-progress-bar">
            <div class="progress-bar">
              <div class="progress-fill savings" [style.width.%]="savingsData().overallProgress"></div>
            </div>
            <span class="progress-text">{{ savingsData().overallProgress }}% complete</span>
          </div>
        </div>

        <!-- Loyalty Cards Widget -->
        <div *ngIf="widget.type === widgetTypes.LoyaltyCards" class="loyalty-cards">
          <div class="cards-count">
            <i class="fas fa-credit-card"></i>
            <span class="count">{{ loyaltyCards().length }}</span>
            <span class="label">Loyalty Cards</span>
          </div>
          <div class="cards-preview" *ngIf="loyaltyCards().length > 0">
            <div class="card-item" *ngFor="let card of loyaltyCards().slice(0, 3)">
              <div class="card-color" [style.background-color]="card.color"></div>
              <span class="card-name">{{ card.name }}</span>
            </div>
          </div>
        </div>

        <!-- Recent Transactions Widget -->
        <div *ngIf="widget.type === widgetTypes.RecentTransactions" class="recent-transactions">
          <div class="transactions-list">
            <div class="transaction-item" *ngFor="let transaction of recentTransactions">
              <div class="transaction-info">
                <span class="transaction-description">{{ transaction.description }}</span>
                <span class="transaction-date">{{ transaction.date | date:'short' }}</span>
              </div>
              <span class="transaction-amount" [class.negative]="transaction.amount < 0">
                {{ transaction.amount | currency:'TND':'symbol':'1.2-2' }}
              </span>
            </div>
          </div>
        </div>

        <!-- Quick Actions Widget -->
        <div *ngIf="widget.type === widgetTypes.QuickActions" class="quick-actions">
          <div class="action-buttons">
            <button class="action-btn" (click)="onQuickAction('add-expense')">
              <i class="fas fa-plus"></i>
              <span>Add Expense</span>
            </button>
            <button class="action-btn" (click)="onQuickAction('add-income')">
              <i class="fas fa-arrow-up"></i>
              <span>Add Income</span>
            </button>
            <button class="action-btn" (click)="onQuickAction('view-budget')">
              <i class="fas fa-chart-pie"></i>
              <span>View Budget</span>
            </button>
            <button class="action-btn" (click)="onQuickAction('view-savings')">
              <i class="fas fa-piggy-bank"></i>
              <span>View Savings</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./dashboard-widget.component.scss']
})
export class DashboardWidgetComponent {
  @Input() widget!: DashboardWidget;
  @Output() widgetAction = new EventEmitter<{action: string, widgetId: string, data?: any}>();

  // Inject services
  private dashboardDataService = inject(DashboardDataService);
  private budgetDataService = inject(BudgetDataService);
  private financialSavingsDataService = inject(FinancialSavingsDataService);
  private loyaltyCardDataService = inject(LoyaltyCardDataService);

  // Widget types enum for template
  widgetTypes = DashboardWidgetType;

  // Computed data from services
  budgetData = computed(() => this.budgetDataService.budgetState());
  savingsData = computed(() => this.financialSavingsDataService.savingPlanState());
  loyaltyCards = computed(() => this.loyaltyCardDataService.cards());

  // Mock data for demonstration
  recentTransactions = [
    { description: 'Grocery Shopping', amount: -45.50, date: new Date() },
    { description: 'Salary Deposit', amount: 2500.00, date: new Date() },
    { description: 'Coffee Shop', amount: -8.75, date: new Date() }
  ];

  onRefresh() {
    this.widgetAction.emit({ action: 'refresh', widgetId: this.widget.id });
  }

  onSettings() {
    this.widgetAction.emit({ action: 'settings', widgetId: this.widget.id });
  }

  onRemove() {
    this.widgetAction.emit({ action: 'remove', widgetId: this.widget.id });
  }

  onQuickAction(action: string) {
    this.widgetAction.emit({ action: 'quick-action', widgetId: this.widget.id, data: action });
  }
}
