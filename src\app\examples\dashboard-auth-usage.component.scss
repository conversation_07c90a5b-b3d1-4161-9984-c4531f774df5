.dashboard-auth-example {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  h2 {
    color: #333;
    margin-bottom: 30px;
    text-align: center;
    font-size: 2rem;
    font-weight: 600;
  }

  h3 {
    color: #444;
    margin-bottom: 15px;
    font-size: 1.3rem;
    font-weight: 500;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
  }

  h4 {
    color: #555;
    margin-bottom: 10px;
    font-size: 1.1rem;
    font-weight: 500;
  }
}

// Section Styles
.auth-section,
.dashboard-section,
.analytics-section,
.widgets-section,
.actions-section,
.preferences-section,
.error-section,
.not-auth-section {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

// Auth Info
.auth-info,
.dashboard-info,
.preferences-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;

  p {
    margin: 8px 0;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #007bff;

    strong {
      color: #333;
      font-weight: 600;
    }
  }
}

// Analytics Grid
.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;

  .analytics-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

    h4 {
      margin-bottom: 15px;
      font-size: 1.2rem;
      color: white;
    }

    p {
      margin: 8px 0;
      font-size: 1rem;
      opacity: 0.9;
    }
  }
}

// Widgets List
.widgets-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;

  .widget-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    border: 1px solid #dee2e6;
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .widget-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      h4 {
        margin: 0;
        color: #333;
      }

      .widget-type {
        background: #007bff;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
      }
    }

    .widget-details {
      margin-bottom: 15px;

      p {
        margin: 5px 0;
        font-size: 0.9rem;
        color: #666;
      }
    }

    .widget-actions {
      display: flex;
      gap: 10px;
    }
  }
}

// Action Buttons
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.btn-primary,
.btn-secondary,
.btn-toggle,
.btn-remove {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background: #007bff;
  color: white;

  &:hover {
    background: #0056b3;
    transform: translateY(-1px);
  }
}

.btn-secondary {
  background: #6c757d;
  color: white;

  &:hover {
    background: #545b62;
    transform: translateY(-1px);
  }
}

.btn-toggle {
  background: #28a745;
  color: white;
  padding: 6px 12px;
  font-size: 0.8rem;

  &:hover {
    background: #1e7e34;
  }
}

.btn-remove {
  background: #dc3545;
  color: white;
  padding: 6px 12px;
  font-size: 0.8rem;

  &:hover {
    background: #c82333;
  }
}

// Notifications Settings
.notifications-settings {
  margin-top: 20px;

  ul {
    list-style: none;
    padding: 0;
    margin: 10px 0;

    li {
      padding: 8px 12px;
      margin: 5px 0;
      background: #f8f9fa;
      border-radius: 6px;
      border-left: 4px solid #28a745;
      font-size: 0.9rem;
    }
  }
}

// Error Section
.error-section {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;

  .error-message {
    margin: 0;
    font-weight: 500;
  }
}

// Not Authenticated Section
.not-auth-section {
  text-align: center;
  background: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;

  p {
    margin-bottom: 20px;
    font-size: 1.1rem;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .dashboard-auth-example {
    padding: 15px;

    h2 {
      font-size: 1.5rem;
    }
  }

  .auth-info,
  .dashboard-info,
  .preferences-info {
    grid-template-columns: 1fr;
  }

  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .widgets-list {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;

    .btn-primary,
    .btn-secondary {
      width: 100%;
    }
  }

  .widget-actions {
    flex-direction: column;

    .btn-toggle,
    .btn-remove {
      width: 100%;
    }
  }
}
