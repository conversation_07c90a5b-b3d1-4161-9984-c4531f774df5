import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number; // in milliseconds, default 4000
  showCloseButton?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ToastService {
  private toastsSubject = new BehaviorSubject<ToastMessage[]>([]);
  public toasts$: Observable<ToastMessage[]> = this.toastsSubject.asObservable();

  constructor() {}

  /**
   * Show a success toast message
   */
  showSuccess(title: string, message: string, duration: number = 4000): void {
    this.addToast({
      type: 'success',
      title,
      message,
      duration
    });
  }

  /**
   * Show an error toast message
   */
  showError(title: string, message: string, duration: number = 6000): void {
    this.addToast({
      type: 'error',
      title,
      message,
      duration,
      showCloseButton: true
    });
  }

  /**
   * Show a warning toast message
   */
  showWarning(title: string, message: string, duration: number = 5000): void {
    this.addToast({
      type: 'warning',
      title,
      message,
      duration,
      showCloseButton: true
    });
  }

  /**
   * Show an info toast message
   */
  showInfo(title: string, message: string, duration: number = 4000): void {
    this.addToast({
      type: 'info',
      title,
      message,
      duration
    });
  }

  /**
   * Add a toast message
   */
  private addToast(toast: Omit<ToastMessage, 'id'>): void {
    const id = this.generateId();
    const newToast: ToastMessage = {
      ...toast,
      id
    };

    const currentToasts = this.toastsSubject.value;
    this.toastsSubject.next([...currentToasts, newToast]);

    // Auto-remove toast after duration
    if (toast.duration && toast.duration > 0) {
      setTimeout(() => {
        this.removeToast(id);
      }, toast.duration);
    }
  }

  /**
   * Remove a specific toast
   */
  removeToast(id: string): void {
    const currentToasts = this.toastsSubject.value;
    const updatedToasts = currentToasts.filter(toast => toast.id !== id);
    this.toastsSubject.next(updatedToasts);
  }

  /**
   * Clear all toasts
   */
  clearAll(): void {
    this.toastsSubject.next([]);
  }

  /**
   * Generate a unique ID for toasts
   */
  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }
}
