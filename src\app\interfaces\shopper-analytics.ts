export interface ShopperAnalyticsData {
  id: string;
  userId: string; // Email of the user who owns this analytics data
  lastUpdated: Date;
  savingsGoalProgress: SavingsGoalProgressData;
  budgetSpentProgress: BudgetSpentProgressData;
  spendingSection: SpendingSectionData;
  statsGrid: StatsGridData;
  categoryExpenses: CategoryExpensesData;
  productExpenses: ProductExpensesData;
}

export interface SavingsGoalProgressData {
  userId: string;
  totalGoal: number;
  totalCurrent: number;
  percentage: number;
  currency: string;
  plans: SavingsGoalItem[];
}

export interface SavingsGoalItem {
  id: string;
  name: string;
  goal: number;
  current: number;
  percentage: number;
  color: string;
  isActive: boolean;
}

export interface BudgetSpentProgressData {
  userId: string;
  totalBudget: number;
  totalSpent: number;
  percentage: number;
  currency: string;
  categories: BudgetSpentItem[];
}

export interface BudgetSpentItem {
  id: string;
  name: string;
  budget: number;
  spent: number;
  percentage: number;
  color: string;
  isOverBudget: boolean;
}

export interface SpendingSectionData {
  userId: string;
  monthlySpending: MonthlySpendingData;
  trendData: number[];
  currency: string;
}

export interface MonthlySpendingData {
  amount: number;
  percentage: number;
  percentageChange: number;
  comparedPeriod: string;
  monthlyBreakdown: MonthlyBreakdownItem[];
}

export interface MonthlyBreakdownItem {
  month: string;
  year: number;
  amount: number;
  transactionCount: number;
}

export interface StatsGridData {
  userId: string;
  monthlyPercentage: number;
  topCategory: CategoryStatsItem;
  totalTransactions: number;
  averageTransaction: number;
  currency: string;
}

export interface CategoryStatsItem {
  name: string;
  amount: number;
  percentage: number;
  transactionCount: number;
}

export interface CategoryExpensesData {
  userId: string;
  categories: CategoryExpenseItem[];
  totalAmount: number;
  currency: string;
}

export interface CategoryExpenseItem {
  name: string;
  amount: number;
  percentage: number;
  transactionCount: number;
  color: string;
  trend: 'up' | 'down' | 'stable';
}

export interface ProductExpensesData {
  userId: string;
  products: ProductExpenseItem[];
  totalAmount: number;
  currency: string;
}

export interface ProductExpenseItem {
  name: string;
  amount: number;
  percentage: number;
  transactionCount: number;
  category: string;
  trend: 'up' | 'down' | 'stable';
}

export interface ShopperAnalyticsState {
  analytics: ShopperAnalyticsData | null;
  savingsGoalProgress: SavingsGoalProgressData | null;
  budgetSpentProgress: BudgetSpentProgressData | null;
  spendingSection: SpendingSectionData | null;
  statsGrid: StatsGridData | null;
  categoryExpenses: CategoryExpensesData | null;
  productExpenses: ProductExpensesData | null;
  isLoading: boolean;
  lastSync: Date | null;
  error: string | null;
}

// Type guards for ShopperAnalytics interfaces
export function isShopperAnalyticsData(obj: any): obj is ShopperAnalyticsData {
  return obj && typeof obj === 'object' && 'id' in obj && 'userId' in obj && 'savingsGoalProgress' in obj;
}

export function isSavingsGoalProgressData(obj: any): obj is SavingsGoalProgressData {
  return obj && typeof obj === 'object' && 'userId' in obj && 'totalGoal' in obj && 'plans' in obj;
}

export function isBudgetSpentProgressData(obj: any): obj is BudgetSpentProgressData {
  return obj && typeof obj === 'object' && 'userId' in obj && 'totalBudget' in obj && 'categories' in obj;
}

export function isSpendingSectionData(obj: any): obj is SpendingSectionData {
  return obj && typeof obj === 'object' && 'userId' in obj && 'monthlySpending' in obj && 'trendData' in obj;
}

export function isStatsGridData(obj: any): obj is StatsGridData {
  return obj && typeof obj === 'object' && 'userId' in obj && 'monthlyPercentage' in obj && 'topCategory' in obj;
}

export function isCategoryExpensesData(obj: any): obj is CategoryExpensesData {
  return obj && typeof obj === 'object' && 'userId' in obj && 'categories' in obj && 'totalAmount' in obj;
}

export function isProductExpensesData(obj: any): obj is ProductExpensesData {
  return obj && typeof obj === 'object' && 'userId' in obj && 'products' in obj && 'totalAmount' in obj;
}

export function isShopperAnalyticsState(obj: any): obj is ShopperAnalyticsState {
  return obj && typeof obj === 'object' && 'isLoading' in obj && 'error' in obj;
}

// Default configuration for shopper analytics
export const DEFAULT_SHOPPER_ANALYTICS_CONFIG: Partial<ShopperAnalyticsData> = {
  savingsGoalProgress: {
    userId: '',
    totalGoal: 0,
    totalCurrent: 0,
    percentage: 0,
    currency: 'TND',
    plans: []
  },
  budgetSpentProgress: {
    userId: '',
    totalBudget: 0,
    totalSpent: 0,
    percentage: 0,
    currency: 'TND',
    categories: []
  },
  spendingSection: {
    userId: '',
    monthlySpending: {
      amount: 0,
      percentage: 0,
      percentageChange: 0,
      comparedPeriod: 'last month',
      monthlyBreakdown: []
    },
    trendData: [],
    currency: 'TND'
  },
  statsGrid: {
    userId: '',
    monthlyPercentage: 0,
    topCategory: {
      name: 'No Data',
      amount: 0,
      percentage: 0,
      transactionCount: 0
    },
    totalTransactions: 0,
    averageTransaction: 0,
    currency: 'TND'
  },
  categoryExpenses: {
    userId: '',
    categories: [],
    totalAmount: 0,
    currency: 'TND'
  },
  productExpenses: {
    userId: '',
    products: [],
    totalAmount: 0,
    currency: 'TND'
  }
};
