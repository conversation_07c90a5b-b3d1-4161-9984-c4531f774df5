import { Component, inject, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DashboardDataService } from '../core/dashboard/dashboard-data.service';
import { AuthService } from '../core/auth/auth.service';
import { DashboardWidgetType } from '../interfaces/dashboard';

/**
 * Example component demonstrating how to use the new signal-based dashboard auth interface
 * This shows how the dashboard integrates with authentication and provides user-specific data
 */
@Component({
  selector: 'app-dashboard-auth-usage',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="dashboard-auth-example">
      <h2>Dashboard Auth Interface Example</h2>
      
      <!-- Authentication Status -->
      <div class="auth-section">
        <h3>Authentication Status</h3>
        <div class="auth-info">
          <p><strong>Authenticated:</strong> {{ isAuthenticated() ? 'Yes' : 'No' }}</p>
          <p><strong>Current User:</strong> {{ currentUser()?.full_name || 'None' }}</p>
          <p><strong>Email:</strong> {{ currentUser()?.email || 'None' }}</p>
        </div>
      </div>

      <!-- Dashboard State -->
      <div class="dashboard-section" *ngIf="isAuthenticated()">
        <h3>Dashboard State</h3>
        <div class="dashboard-info">
          <p><strong>Loading:</strong> {{ isLoading() ? 'Yes' : 'No' }}</p>
          <p><strong>Dashboard ID:</strong> {{ dashboard()?.id || 'None' }}</p>
          <p><strong>Widgets Count:</strong> {{ widgets().length }}</p>
          <p><strong>Has Unsaved Changes:</strong> {{ hasUnsavedChanges() ? 'Yes' : 'No' }}</p>
          <p><strong>Last Updated:</strong> {{ dashboard()?.lastUpdated | date:'medium' }}</p>
        </div>
      </div>

      <!-- Dashboard Analytics -->
      <div class="analytics-section" *ngIf="isAuthenticated() && !isLoading()">
        <h3>Dashboard Analytics</h3>
        <div class="analytics-grid">
          <div class="analytics-card">
            <h4>Budget</h4>
            <p>Total: {{ analytics().totalBudget | currency:'TND':'symbol':'1.2-2' }}</p>
            <p>Spent: {{ analytics().totalSpent | currency:'TND':'symbol':'1.2-2' }}</p>
          </div>
          <div class="analytics-card">
            <h4>Savings</h4>
            <p>Total: {{ analytics().totalSavings | currency:'TND':'symbol':'1.2-2' }}</p>
          </div>
          <div class="analytics-card">
            <h4>Loyalty Cards</h4>
            <p>Count: {{ analytics().loyaltyCardsCount }}</p>
          </div>
        </div>
      </div>

      <!-- Widgets List -->
      <div class="widgets-section" *ngIf="isAuthenticated() && !isLoading()">
        <h3>Dashboard Widgets</h3>
        <div class="widgets-list">
          <div class="widget-item" *ngFor="let widget of widgets()">
            <div class="widget-header">
              <h4>{{ widget.title }}</h4>
              <span class="widget-type">{{ widget.type }}</span>
            </div>
            <div class="widget-details">
              <p>Position: Row {{ widget.position.row }}, Col {{ widget.position.column }}</p>
              <p>Size: {{ widget.size.width }}x{{ widget.size.height }}</p>
              <p>Visible: {{ widget.isVisible ? 'Yes' : 'No' }}</p>
            </div>
            <div class="widget-actions">
              <button (click)="toggleWidgetVisibility(widget.id)" class="btn-toggle">
                {{ widget.isVisible ? 'Hide' : 'Show' }}
              </button>
              <button (click)="removeWidget(widget.id)" class="btn-remove">Remove</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Dashboard Actions -->
      <div class="actions-section" *ngIf="isAuthenticated()">
        <h3>Dashboard Actions</h3>
        <div class="action-buttons">
          <button (click)="addSampleWidget()" class="btn-primary">Add Sample Widget</button>
          <button (click)="updatePreferences()" class="btn-secondary">Update Preferences</button>
          <button (click)="refreshDashboard()" class="btn-secondary">Refresh Dashboard</button>
        </div>
      </div>

      <!-- User Preferences -->
      <div class="preferences-section" *ngIf="isAuthenticated() && dashboard()">
        <h3>User Preferences</h3>
        <div class="preferences-info">
          <p><strong>Theme:</strong> {{ dashboard()!.preferences.theme }}</p>
          <p><strong>Currency:</strong> {{ dashboard()!.preferences.currency }}</p>
          <p><strong>Language:</strong> {{ dashboard()!.preferences.language }}</p>
          <p><strong>Default View:</strong> {{ dashboard()!.preferences.defaultView }}</p>
        </div>
        <div class="notifications-settings">
          <h4>Notification Settings</h4>
          <ul>
            <li>Budget Alerts: {{ dashboard()!.preferences.notifications.budgetAlerts ? 'On' : 'Off' }}</li>
            <li>Savings Reminders: {{ dashboard()!.preferences.notifications.savingsReminders ? 'On' : 'Off' }}</li>
            <li>Loyalty Card Expiry: {{ dashboard()!.preferences.notifications.loyaltyCardExpiry ? 'On' : 'Off' }}</li>
            <li>Weekly Reports: {{ dashboard()!.preferences.notifications.weeklyReports ? 'On' : 'Off' }}</li>
            <li>Monthly Reports: {{ dashboard()!.preferences.notifications.monthlyReports ? 'On' : 'Off' }}</li>
          </ul>
        </div>
      </div>

      <!-- Error Display -->
      <div class="error-section" *ngIf="error()">
        <h3>Error</h3>
        <p class="error-message">{{ error() }}</p>
      </div>

      <!-- Not Authenticated Message -->
      <div class="not-auth-section" *ngIf="!isAuthenticated()">
        <h3>Authentication Required</h3>
        <p>Please log in to see your personalized dashboard data.</p>
        <button routerLink="/login" class="btn-primary">Go to Login</button>
      </div>
    </div>
  `,
  styleUrls: ['./dashboard-auth-usage.component.scss']
})
export class DashboardAuthUsageComponent {
  // Inject services
  private dashboardDataService = inject(DashboardDataService);
  private authService = inject(AuthService);

  // Signal-based reactive data
  isAuthenticated = computed(() => this.authService.isAuthenticated());
  currentUser = computed(() => this.authService.currentUser());
  dashboard = computed(() => this.dashboardDataService.dashboard());
  widgets = computed(() => this.dashboardDataService.widgets());
  analytics = computed(() => this.dashboardDataService.analytics());
  isLoading = computed(() => this.dashboardDataService.isLoading());
  hasUnsavedChanges = computed(() => this.dashboardDataService.hasUnsavedChanges());
  error = computed(() => this.dashboardDataService.error());

  // Example methods to demonstrate dashboard functionality
  addSampleWidget() {
    const sampleWidget = {
      type: DashboardWidgetType.QuickActions,
      title: `Sample Widget ${Date.now()}`,
      position: { row: 0, column: 0, order: 999 },
      size: { width: 4, height: 2 },
      isVisible: true,
      config: { 
        refreshInterval: 30000, 
        showHeader: true, 
        allowResize: true, 
        allowMove: true 
      }
    };

    this.dashboardDataService.addWidget(sampleWidget).subscribe(widget => {
      console.log('Added sample widget:', widget);
    });
  }

  toggleWidgetVisibility(widgetId: string) {
    const widget = this.widgets().find(w => w.id === widgetId);
    if (widget) {
      this.dashboardDataService.updateWidget(widgetId, { 
        isVisible: !widget.isVisible 
      }).subscribe(updatedWidget => {
        console.log('Toggled widget visibility:', updatedWidget);
      });
    }
  }

  removeWidget(widgetId: string) {
    this.dashboardDataService.removeWidget(widgetId).subscribe(success => {
      console.log('Removed widget:', success);
    });
  }

  updatePreferences() {
    const newPreferences = {
      theme: 'dark' as const,
      notifications: {
        budgetAlerts: true,
        savingsReminders: true,
        loyaltyCardExpiry: false,
        weeklyReports: true,
        monthlyReports: true
      }
    };

    this.dashboardDataService.updateDashboardPreferences(newPreferences).subscribe(success => {
      console.log('Updated preferences:', success);
    });
  }

  refreshDashboard() {
    // This would typically trigger a refresh of all dashboard data
    console.log('Refreshing dashboard data...');
    // In a real implementation, you might call specific refresh methods
  }
}
