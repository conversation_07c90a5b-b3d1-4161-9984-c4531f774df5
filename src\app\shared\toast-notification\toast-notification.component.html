<!-- Toast Container -->
<div class="toast-container" [class.dark-mode]="isDarkMode">
  <div 
    *ngFor="let toast of toasts; trackBy: trackByToastId" 
    class="toast-message"
    [class.toast-success]="toast.type === 'success'"
    [class.toast-error]="toast.type === 'error'"
    [class.toast-warning]="toast.type === 'warning'"
    [class.toast-info]="toast.type === 'info'"
    [@slideInOut]>
    
    <!-- Toast Icon -->
    <div class="toast-icon">
      <i [class]="getToastIcon(toast.type)"></i>
    </div>
    
    <!-- Toast Content -->
    <div class="toast-content">
      <div class="toast-title">{{ toast.title }}</div>
      <div class="toast-message-text">{{ toast.message }}</div>
    </div>
    
    <!-- Close Button -->
    <button 
      *ngIf="toast.showCloseButton !== false"
      class="toast-close-btn"
      (click)="removeToast(toast.id)"
      aria-label="Close notification">
      <i class="fas fa-times"></i>
    </button>
  </div>
</div>
