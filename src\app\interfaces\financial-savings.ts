export interface SavingPlanData {
  id: string;
  name: string;
  objective: number; // Target amount to save
  current: number;   // Current saved amount
  period: SavingPeriod;
  frequency: SavingFrequency;
  startDate: Date;
  endDate?: Date;    // Optional specific end date
  interestRate?: number; // Optional interest rate
  lastUpdate: Date;
  percentComplete?: number; // Calculated percentage of completion
  // Duration savings properties
  durationAmount?: number; // Amount to save per duration period
  durationValue?: number;  // Number of duration periods
  durationType?: string;   // Type of duration (weeks, months, years)
  userId: string; // Email of the user who owns this saving plan
}

export interface SavingPlanState {
  plans: SavingPlanData[];
  totalPlans: number;
  totalObjective: number;
  totalCurrent: number;
  totalRemaining: number;
  overallProgress: number;
  isLoading: boolean;
}

export enum SavingPeriod {
  ThreeMonths = '3 months',
  SixMonths = '6 months',
  NineMonths = '9 months',
  OneYear = '1 year',
  Custom = 'Custom'
}

export enum SavingFrequency {
  Daily = 'Daily',
  Weekly = 'Weekly',
  Monthly = 'Monthly',
  OneTime = 'Just this once'
}

// Type guard for SavingPlanData
export function isSavingPlanData(obj: any): obj is SavingPlanData {
  return obj && typeof obj === 'object' && 'id' in obj && 'name' in obj && 'userId' in obj;
}
