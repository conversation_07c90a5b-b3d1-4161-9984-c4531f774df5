import { Injectable, signal, computed, effect, inject } from '@angular/core';
import { Observable, of } from 'rxjs';
import { LoyaltyCardData, LoyaltyCardState, CARD_COLORS } from '../../interfaces/loyalty-card';
import { AuthService } from '../auth/auth.service';

@Injectable({
  providedIn: 'root'
})
export class LoyaltyCardDataService {
  private authService = inject(AuthService);

  // Private signals for loyalty card data
  private _loyaltyCardState = signal<LoyaltyCardState>({
    cards: [],
    totalCards: 0,
    isLoading: false
  });

  private _currentUserEmail = signal<string | null>(null);

  // Public readonly signals
  public readonly loyaltyCardState = this._loyaltyCardState.asReadonly();
  public readonly currentUserEmail = this._currentUserEmail.asReadonly();

  // Computed signals for derived state
  public readonly cards = computed(() => this._loyaltyCardState().cards);
  public readonly totalCards = computed(() => this._loyaltyCardState().totalCards);
  public readonly isLoading = computed(() => this._loyaltyCardState().isLoading);

  constructor() {
    // Watch for auth user changes and load corresponding loyalty card data using effect
    effect(() => {
      const authUser = this.authService.currentUser();
      console.log('LoyaltyCardDataService effect triggered - authUser:', authUser);
      if (authUser) {
        this._currentUserEmail.set(authUser.email);
        this.loadUserLoyaltyCardData(authUser.email);
      } else {
        this._currentUserEmail.set(null);
        this.clearLoyaltyCardData();
      }
    }, { allowSignalWrites: true });

    // Effect to save loyalty card data when it changes
    effect(() => {
      const loyaltyCardState = this._loyaltyCardState();
      const userEmail = this._currentUserEmail();
      if (userEmail && loyaltyCardState.cards.length > 0) {
        this.saveLoyaltyCardDataToStorage(loyaltyCardState, userEmail);
      }
    }, { allowSignalWrites: true });
  }

  /**
   * Load user-specific loyalty card data from storage
   */
  private loadUserLoyaltyCardData(userEmail: string): void {
    this._loyaltyCardState.update(state => ({ ...state, isLoading: true }));

    try {
      const storageKey = `loyalty_cards_${userEmail}`;
      const storedData = localStorage.getItem(storageKey);
      
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        const cards: LoyaltyCardData[] = parsedData.cards?.map((card: any) => ({
          ...card,
          createdAt: new Date(card.createdAt)
        })) || [];

        this._loyaltyCardState.set({
          cards,
          totalCards: cards.length,
          isLoading: false
        });

        console.log(`Loaded ${cards.length} loyalty cards for user: ${userEmail}`);
      } else {
        this.clearLoyaltyCardData();
      }
    } catch (error) {
      console.error('Error loading loyalty card data:', error);
      this.clearLoyaltyCardData();
    }
  }

  /**
   * Save loyalty card data to storage for the current user
   */
  private saveLoyaltyCardDataToStorage(loyaltyCardState: LoyaltyCardState, userEmail: string): void {
    try {
      const storageKey = `loyalty_cards_${userEmail}`;
      const dataToSave = {
        cards: loyaltyCardState.cards,
        lastUpdated: new Date().toISOString()
      };
      
      localStorage.setItem(storageKey, JSON.stringify(dataToSave));
      console.log(`Saved loyalty card data for user: ${userEmail}`);
    } catch (error) {
      console.error('Error saving loyalty card data:', error);
    }
  }

  /**
   * Clear all loyalty card data
   */
  private clearLoyaltyCardData(): void {
    this._loyaltyCardState.set({
      cards: [],
      totalCards: 0,
      isLoading: false
    });
  }

  /**
   * Get available card colors
   */
  getAvailableColors(): string[] {
    return [...CARD_COLORS];
  }

  /**
   * Add a new loyalty card
   */
  addLoyaltyCard(card: Omit<LoyaltyCardData, 'id' | 'createdAt' | 'userId'>): Observable<LoyaltyCardData> {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot add loyalty card: No authenticated user');
      return of(null as any);
    }

    const newCard: LoyaltyCardData = {
      ...card,
      id: Date.now().toString(),
      createdAt: new Date(),
      userId: currentUser.email
    };

    this._loyaltyCardState.update(state => {
      const updatedCards = [newCard, ...state.cards];
      return {
        ...state,
        cards: updatedCards,
        totalCards: updatedCards.length
      };
    });

    console.log('Added new loyalty card:', newCard);
    return of(newCard);
  }

  /**
   * Update an existing loyalty card
   */
  updateLoyaltyCard(updatedCard: LoyaltyCardData): Observable<LoyaltyCardData> {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot update loyalty card: No authenticated user');
      return of(null as any);
    }

    // Ensure the card belongs to the current user
    if (updatedCard.userId !== currentUser.email) {
      console.error('Cannot update loyalty card: Card does not belong to current user');
      return of(null as any);
    }

    this._loyaltyCardState.update(state => {
      const cardIndex = state.cards.findIndex(card => card.id === updatedCard.id);
      if (cardIndex === -1) {
        console.error('Card not found for update');
        return state;
      }

      const updatedCards = [...state.cards];
      updatedCards[cardIndex] = updatedCard;

      return {
        ...state,
        cards: updatedCards
      };
    });

    console.log('Updated loyalty card:', updatedCard);
    return of(updatedCard);
  }

  /**
   * Delete a loyalty card
   */
  deleteLoyaltyCard(cardId: string): Observable<boolean> {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot delete loyalty card: No authenticated user');
      return of(false);
    }

    this._loyaltyCardState.update(state => {
      const cardToDelete = state.cards.find(card => card.id === cardId);
      
      // Ensure the card belongs to the current user
      if (cardToDelete && cardToDelete.userId !== currentUser.email) {
        console.error('Cannot delete loyalty card: Card does not belong to current user');
        return state;
      }

      const updatedCards = state.cards.filter(card => card.id !== cardId);
      return {
        ...state,
        cards: updatedCards,
        totalCards: updatedCards.length
      };
    });

    console.log('Deleted loyalty card with ID:', cardId);
    return of(true);
  }

  /**
   * Get a specific loyalty card by ID
   */
  getLoyaltyCardById(cardId: string): LoyaltyCardData | undefined {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      return undefined;
    }

    const card = this._loyaltyCardState().cards.find(card => card.id === cardId);
    
    // Ensure the card belongs to the current user
    if (card && card.userId !== currentUser.email) {
      return undefined;
    }

    return card;
  }

  /**
   * Update card barcode image
   */
  updateCardBarcode(cardId: string, barcodeImage: string): Observable<boolean> {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot update card barcode: No authenticated user');
      return of(false);
    }

    this._loyaltyCardState.update(state => {
      const cardIndex = state.cards.findIndex(card => card.id === cardId);
      if (cardIndex === -1) {
        console.error('Card not found for barcode update');
        return state;
      }

      const card = state.cards[cardIndex];
      
      // Ensure the card belongs to the current user
      if (card.userId !== currentUser.email) {
        console.error('Cannot update card barcode: Card does not belong to current user');
        return state;
      }

      const updatedCards = [...state.cards];
      updatedCards[cardIndex] = {
        ...card,
        barcodeImage
      };

      return {
        ...state,
        cards: updatedCards
      };
    });

    console.log('Updated barcode for card ID:', cardId);
    return of(true);
  }
}
