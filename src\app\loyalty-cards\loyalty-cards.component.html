<app-top-navbar></app-top-navbar>
<app-sidebar></app-sidebar>

<!-- Authentication Warning -->
<app-auth-warning></app-auth-warning>

<div class="loyalty-cards-container" *ngIf="isAuthenticated()">
  <div class="header">
    <h1 class="loyalty-title">Loyalty Cards</h1>
  </div>

  <div class="content">
    <!-- Empty state when no cards exist -->
    <div *ngIf="!isLoading && loyaltyCards.length === 0" class="empty-state">
      <div class="empty-illustration">
        <div class="background-circles">
          <div class="circle circle-1"></div>
          <div class="circle circle-2"></div>
          <div class="circle circle-3"></div>
        </div>
        <div class="main-content">
          <div class="sad-face">
            <div class="face">
              <div class="eyes">
                <div class="eye left">
                  <div class="pupil"></div>
                </div>
                <div class="eye right">
                  <div class="pupil"></div>
                </div>
              </div>
              <div class="mouth">
                <svg viewBox="0 0 60 30" class="mouth-svg">
                  <path d="M10,10 Q30,25 50,10" stroke="currentColor" stroke-width="3" fill="none" stroke-linecap="round"/>
                </svg>
              </div>
            </div>
            <div class="tears">
              <div class="tear tear-1"></div>
              <div class="tear tear-2"></div>
            </div>
          </div>
          <div class="empty-content">
            <h3 class="empty-title">No Loyalty Cards Yet</h3>
            <p class="empty-subtitle">Start building your collection by adding your first loyalty card</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Cards list when cards exist -->
    <div *ngIf="!isLoading && loyaltyCards.length > 0" class="cards-list">
      <div *ngFor="let card of loyaltyCards" class="card-item">
        <div class="card-header" [ngStyle]="getCardStyle(card.color)">
          <span class="card-name">{{ card.name }}</span>
          <div class="card-options-wrapper">
            <button class="card-options" (click)="toggleCardOptions($event, card)">
              <i class="fas fa-ellipsis-v"></i>
            </button>
            <div *ngIf="card.showOptions" class="card-options-menu">
              <button class="option-item upload-barcode" (click)="uploadBarcode($event, card)">
                <i class="fas fa-upload"></i> Upload Barcode
              </button>
              <button class="option-item delete-card" (click)="deleteCard($event, card)">
                <i class="fas fa-trash"></i> Delete Card
              </button>
            </div>
          </div>
        </div>
        <div class="card-body bg-white" style="background-color: white !important;" [routerLink]="['/loyalty-card', card.id]">
          <div class="barcode">
            <!-- Barcode display -->
            <div class="barcode-image">
              <!-- Show uploaded barcode image if available, otherwise show generated barcode -->
              <ng-container *ngIf="card.barcodeImage; else generatedBarcode">
                <img [src]="card.barcodeImage" alt="Barcode" class="uploaded-barcode">
              </ng-container>
              <ng-template #generatedBarcode>
                <!-- Vertical black bars to simulate barcode -->
                <div *ngFor="let i of [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29]"
                     class="barcode-line"
                     [style.width.px]="1 + i % 3"
                     [style.margin-right.px]="1">
                </div>
              </ng-template>
            </div>
            <div class="card-number">{{ card.cardNumber }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Add Card Button -->
  <button class="add-card-btn" (click)="addNewCard()">ADD LOYALTY CARD</button>

  <!-- Hidden file input for barcode upload -->
  <input
    type="file"
    #barcodeFileInput
    style="display: none;"
    accept="image/*"
    (change)="onBarcodeFileSelected($event)">
</div>