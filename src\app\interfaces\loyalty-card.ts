export interface LoyaltyCardData {
  id: string;
  name: string;
  cardNumber: string;
  color: string;
  createdAt: Date;
  barcodeImage?: string;
  userId: string; // Email of the user who owns this card
}

export interface LoyaltyCardState {
  cards: LoyaltyCardData[];
  totalCards: number;
  isLoading: boolean;
}

export const CARD_COLORS = [
  '#4CAF50', // Green
  '#2196F3', // Blue
  '#FF9800', // Orange
  '#E91E63', // Pink
  '#9C27B0', // Purple
  '#795548', // <PERSON>
  '#607D8B'  // Blue Grey
];

// Type guard for LoyaltyCardData
export function isLoyaltyCardData(obj: any): obj is LoyaltyCardData {
  return obj && typeof obj === 'object' && 'id' in obj && 'name' in obj && 'userId' in obj;
}
