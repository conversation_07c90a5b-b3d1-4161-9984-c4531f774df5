<div class="modal-overlay" (click)="closeModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>Confirm Purchase</h2>
      <button class="close-btn" (click)="closeModal()">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="modal-body">
      <div class="product-info">
        <div class="product-image-container">
          <img [src]="productImage || 'assets/images/default-product.jpg'" alt="{{ productName }}" class="product-image">
        </div>

        <div class="product-details">
          <h3>{{ productName }}</h3>
          <div class="brand-container">
            <i class="fas fa-store"></i>
            <p class="brand">{{ brandName }}</p>
          </div>
          <div class="price-container">
            <i class="fas fa-tag"></i>
            <p class="price">{{ formatAmount(productPrice) }} <span class="currency">TND</span></p>
          </div>
        </div>
      </div>

      <div class="quantity-section">
        <div class="section-title">
          <i class="fas fa-shopping-basket"></i>
          <h4>Select Quantity</h4>
        </div>

        <div class="quantity-selector">
          <div class="quantity-controls">
            <button class="quantity-btn decrement" (click)="decrementQuantity()">
              <i class="fas fa-minus"></i>
            </button>
            <input
              type="number"
              id="quantity"
              name="quantity"
              [(ngModel)]="quantity"
              (ngModelChange)="onQuantityChange()"
              min="1"
            >
            <button class="quantity-btn increment" (click)="incrementQuantity()">
              <i class="fas fa-plus"></i>
            </button>
          </div>
        </div>
      </div>

      <div class="total-section">
        <div class="total-label">
          <i class="fas fa-receipt"></i>
          <h3>Total Amount</h3>
        </div>
        <p class="total-price">{{ formatAmount(total) }} <span class="currency">TND</span></p>
      </div>
    </div>

    <div class="modal-footer">
      <button class="cancel-btn" (click)="closeModal()">
        <i class="fas fa-times"></i>
        Cancel
      </button>
      <button class="confirm-btn" (click)="confirmPurchase()">
        <i class="fas fa-check"></i>
        Confirm Purchase
      </button>
    </div>
  </div>
</div>
