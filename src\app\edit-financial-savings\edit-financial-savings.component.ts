import { Component, OnInit, OnDestroy, inject, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule, ActivatedRoute } from '@angular/router';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { SavingPlan, SavingPeriod, SavingFrequency, DefaultSavingAmounts } from '../models/financial-savings.model';
import { FinancialSavingsService } from '../services/financial-savings.service';
import { ThemeService } from '../services/theme.service';
import { AuthWarningComponent } from '../shared/auth-warning/auth-warning.component';
import { AuthService } from '../core/auth/auth.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-edit-financial-savings',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    TopNavbarComponent,
    SidebarComponent,
    AuthWarningComponent
  ],
  templateUrl: './edit-financial-savings.component.html',
  styleUrls: ['./edit-financial-savings.component.scss']
})
export class EditFinancialSavingsComponent implements OnInit, OnDestroy {
  // Inject services
  private savingsService = inject(FinancialSavingsService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private themeService = inject(ThemeService);
  private authService = inject(AuthService);

  // Auth signals
  isAuthenticated = computed(() => this.authService.isAuthenticated());
  currentUser = computed(() => this.authService.currentUser());

  // Expose enums to the template
  SavingPeriod = SavingPeriod;
  SavingFrequency = SavingFrequency;

  savingPlan: SavingPlan = {
    id: '',
    name: '',
    objective: 0,
    current: 0,
    period: SavingPeriod.ThreeMonths,
    frequency: SavingFrequency.Monthly,
    startDate: new Date(),
    lastUpdate: new Date(),
    percentComplete: 0
  };

  // For the UI
  selectedFrequency: SavingFrequency = SavingFrequency.Monthly;
  selectedAmount: number = 500; // Default selected amount
  customAmount: number = 0;
  isCustomAmount: boolean = false;
  specificEndDate: string = '';

  // Default saving amounts
  savingAmounts = DefaultSavingAmounts;

  originalSavingPlan: SavingPlan | null = null;
  isDarkMode: boolean = false;
  isLoading: boolean = true;
  notFound: boolean = false;
  isEditing: boolean = false;
  formSubmitted: boolean = false;

  // Properties for duration calculation
  duration: number = 6; // Default 6 units
  durationUnit: string = 'months'; // Default to months
  monthlyAmount: number = 0;
  additionalAmount: number = 0; // New property for additional monthly savings

  // Properties for duration savings
  durationAmount: number | null = null;
  durationValue: number = 1;
  durationType: string = 'months';
  durationTotal: number = 0;
  totalAmount: number = 0;
  currentSavedAmount: number = 0;

  private destroy$ = new Subject<void>();

  ngOnInit(): void {
    // Subscribe to theme changes
    this.themeService.isDarkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isDark => {
        this.isDarkMode = isDark;
      });

    // Check if we're editing an existing plan
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');

      if (id) {
        this.isEditing = true;
        const plan = this.savingsService.getSavingPlanById(id);

        if (plan) {
          this.savingPlan = { ...plan };
          this.originalSavingPlan = { ...plan };
          this.currentSavedAmount = plan.current || 0;

          // Find and select the matching predefined amount if exists
          const matchingAmount = this.savingAmounts.find(amt => amt.amount === plan.objective);
          if (matchingAmount) {
            this.selectedAmount = plan.objective;
            this.isCustomAmount = matchingAmount.isCustom;
          } else {
            // If no matching predefined amount, select "Other"
            this.selectedAmount = 0;
            this.isCustomAmount = true;
            this.customAmount = plan.objective;
          }

          // Calculate initial total
          this.calculateTotal();

          // Set the frequency
          if (plan.frequency) {
            this.selectedFrequency = plan.frequency;
          }

          // Set UI values based on the plan
          if (plan.endDate) {
            this.specificEndDate = this.formatDateForInput(plan.endDate);
          }

          // Load duration data if available
          if (plan.durationAmount !== undefined) {
            this.durationAmount = plan.durationAmount;
          }
          if (plan.durationValue !== undefined) {
            this.durationValue = plan.durationValue;
          }
          if (plan.durationType) {
            this.durationType = plan.durationType;
          }

          // Recalculate duration total
          this.calculateDurationTotal();

          this.isLoading = false;
        } else {
          this.notFound = true;
          this.isLoading = false;
        }
      } else {
        // Creating a new plan
        this.isEditing = false;
        this.isLoading = false;
        // Calculate initial total
        this.calculateTotal();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Format date for input field (YYYY-MM-DD)
  formatDateForInput(date: Date): string {
    const d = new Date(date);
    let month = '' + (d.getMonth() + 1);
    let day = '' + d.getDate();
    const year = d.getFullYear();

    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;

    return [year, month, day].join('-');
  }

  // Select a saving amount
  selectAmount(amount: number, isCustom: boolean): void {
    // No delay needed for custom amounts
    if (isCustom) {
      this.selectedAmount = 0; // Reset selected amount
      this.isCustomAmount = true;
      this.calculateTotal();
      this.calculateDurationTotal(); // Update duration total
    } else {
      // For predefined amounts, use the delay effect
      setTimeout(() => {
        this.selectedAmount = amount;
        this.isCustomAmount = false;
        this.customAmount = amount;
        this.calculateTotal();
        this.calculateDurationTotal(); // Update duration total
      }, 50);
    }
  }

  // Handle custom amount change
  onCustomAmountChange(newAmount: number): void {
    if (this.isCustomAmount) {
      this.customAmount = newAmount;
      this.selectedAmount = newAmount;
      this.calculateTotal();
      this.calculateDurationTotal(); // Update duration total
    }
  }

  // Select a saving frequency
  selectFrequency(frequency: SavingFrequency): void {
    // Add a small delay to create a visual effect of movement
    setTimeout(() => {
      this.selectedFrequency = frequency;
      this.savingPlan.frequency = frequency;
    }, 50);
  }

  // Select a saving period
  selectPeriod(period: SavingPeriod): void {
    // Add a small delay to create a visual effect of movement
    setTimeout(() => {
      this.savingPlan.period = period;

      // If Custom is selected, show the date picker
      if (period === SavingPeriod.Custom && !this.specificEndDate) {
        // Set a default end date 3 months from now
        const endDate = new Date();
        endDate.setMonth(endDate.getMonth() + 3);
        this.specificEndDate = this.formatDateForInput(endDate);
      }
    }, 50);
  }

  // Calculate duration total
  calculateDurationTotal(): void {
    if (this.durationAmount && this.durationValue) {
      const total = this.durationAmount * this.durationValue;
      const initialAmount = this.isCustomAmount ? this.customAmount : this.selectedAmount;
      this.durationTotal = total + initialAmount;
    } else {
      this.durationTotal = 0;
    }
  }

  // Calculate total savings based on duration
  calculateTotal(): void {
    const baseAmount = this.isCustomAmount ? this.customAmount : this.selectedAmount;
    this.monthlyAmount = baseAmount;

    // Convert duration to months for calculation
    let durationInMonths = this.duration;

    if (this.durationUnit === 'weeks') {
      // Convert weeks to months (approximately 4.33 weeks per month)
      durationInMonths = this.duration / 4.33;
    } else if (this.durationUnit === 'years') {
      // Convert years to months
      durationInMonths = this.duration * 12;
    }

    // Calculate total amount including additional monthly savings
    const additionalSavings = this.additionalAmount * durationInMonths;
    this.totalAmount = baseAmount + additionalSavings + this.currentSavedAmount;

    // Update the saving plan objective
    this.savingPlan.objective = this.totalAmount;
  }

  // Save the saving plan
  saveSavingPlan(): void {
    // Validate saving plan name
    if (!this.savingPlan.name || this.savingPlan.name.trim() === '') {
      return;
    }

    // Calculate the total amount including duration savings
    const baseAmount = this.isCustomAmount ? this.customAmount : this.selectedAmount;
    let totalObjective = baseAmount;

    if (this.durationAmount && this.durationValue) {
      const durationSavings = this.durationAmount * this.durationValue;
      totalObjective += durationSavings;
    }

    // Set the objective based on the total calculation
    this.savingPlan.objective = totalObjective;

    // Save duration data to the saving plan
    this.savingPlan.durationAmount = this.durationAmount || undefined;
    this.savingPlan.durationValue = this.durationValue || undefined;
    this.savingPlan.durationType = this.durationType || undefined;

    // Set end date if custom period is selected
    if (this.savingPlan.period === SavingPeriod.Custom && this.specificEndDate) {
      this.savingPlan.endDate = new Date(this.specificEndDate);
    } else {
      this.savingPlan.endDate = undefined; // Clear end date for non-custom periods

      // Calculate end date based on period
      const endDate = new Date(this.savingPlan.startDate);

      switch (this.savingPlan.period) {
        case SavingPeriod.ThreeMonths:
          endDate.setMonth(endDate.getMonth() + 3);
          break;
        case SavingPeriod.SixMonths:
          endDate.setMonth(endDate.getMonth() + 6);
          break;
        case SavingPeriod.NineMonths:
          endDate.setMonth(endDate.getMonth() + 9);
          break;
        case SavingPeriod.OneYear:
          endDate.setFullYear(endDate.getFullYear() + 1);
          break;
      }

      this.savingPlan.endDate = endDate;
    }

    if (this.isEditing) {
      // Update existing plan
      this.savingsService.updateSavingPlan(this.savingPlan);
    } else {
      // Add new plan
      this.savingsService.addSavingPlan(this.savingPlan);
    }

    // Navigate back to the financial savings page
    this.router.navigate(['/financial-savings']);
  }

  // Delete the saving plan
  deleteSavingPlan(): void {
    if (confirm('Are you sure you want to delete this saving plan?')) {
      this.savingsService.deleteSavingPlan(this.savingPlan.id);
      this.router.navigate(['/financial-savings']);
    }
  }

  // Cancel and go back
  cancel(): void {
    this.router.navigate(['/financial-savings']);
  }

  // Show congratulations screen and save
  showCongratulations(): void {
    this.formSubmitted = true;

    // Validate saving plan name first
    if (!this.savingPlan.name || this.savingPlan.name.trim() === '') {
      return;
    }

    // Save the plan
    this.saveSavingPlan();

    // Navigate back to the main page
    this.router.navigate(['/financial-savings']);
  }

  // Handle input focus
  onDurationFocus(): void {
    if (this.durationAmount === 0) {
      this.durationAmount = null;
    }
  }

  // Handle input blur
  onDurationBlur(): void {
    if (this.durationAmount === null || this.durationAmount === undefined) {
      this.durationAmount = 0;
    }
  }

  // Handle duration value focus
  onDurationValueFocus(): void {
    if (this.durationValue === 1) {
      this.durationValue = null as any;
    }
  }

  // Handle duration value blur
  onDurationValueBlur(): void {
    if (this.durationValue === null || this.durationValue === undefined) {
      this.durationValue = 1;
    }
  }

  // Get duration conversion display
  getDurationConversion(): string {
    if (!this.durationValue || this.durationValue <= 0) {
      return '';
    }

    if (this.durationType === 'weeks' && this.durationValue > 4) {
      const months = Math.round((this.durationValue / 4) * 10) / 10; // 4 weeks per month
      return `${this.durationValue} Weeks (${months} Months)`;
    }

    if (this.durationType === 'months' && this.durationValue > 12) {
      const years = Math.round((this.durationValue / 12) * 10) / 10; // 12 months per year
      return `${this.durationValue} Months (${years} Years)`;
    }

    return '';
  }
}
