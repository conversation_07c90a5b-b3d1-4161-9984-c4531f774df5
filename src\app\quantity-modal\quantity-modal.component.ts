import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-quantity-modal',
  templateUrl: './quantity-modal.component.html',
  styleUrls: ['./quantity-modal.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule]
})
export class QuantityModalComponent implements OnInit {
  @Input() productName: string = '';
  @Input() productPrice: number = 0;
  @Input() brandName: string = '';
  @Input() productImage: string = '';
  @Output() confirm = new EventEmitter<{ quantity: number, total: number }>();
  @Output() cancel = new EventEmitter<void>();

  quantity: number = 1;
  total: number = 0;

  ngOnInit(): void {
    this.calculateTotal();
  }

  calculateTotal(): void {
    this.total = this.quantity * this.productPrice;
  }

  incrementQuantity(): void {
    this.quantity++;
    this.calculateTotal();
  }

  decrementQuantity(): void {
    if (this.quantity > 1) {
      this.quantity--;
      this.calculateTotal();
    }
  }

  onQuantityChange(): void {
    // Ensure quantity is at least 1
    if (this.quantity < 1) {
      this.quantity = 1;
    }
    this.calculateTotal();
  }

  confirmPurchase(): void {
    this.confirm.emit({ quantity: this.quantity, total: this.total });
  }

  closeModal(): void {
    this.cancel.emit();
  }

  // Helper method to format numbers - only show decimals when needed
  formatAmount(amount: number): string {
    if (amount % 1 === 0) {
      // If the number is a whole number, don't show decimals
      return amount.toString();
    } else {
      // If the number has decimals, show up to 2 decimal places
      return amount.toFixed(2);
    }
  }
}
