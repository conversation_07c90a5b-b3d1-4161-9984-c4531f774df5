import { Component, OnInit, OnDestroy, computed, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { BudgetData, BudgetCategoryData } from '../interfaces/budget';
import { BudgetDataService } from '../core/budget/budget-data.service';
import { ThemeService } from '../services/theme.service';
import { AuthWarningComponent } from '../shared/auth-warning/auth-warning.component';
import { AuthService } from '../core/auth/auth.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-budget',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TopNavbarComponent,
    SidebarComponent,
    AuthWarningComponent
  ],
  templateUrl: './budget.component.html',
  styleUrls: ['./budget.component.scss']
})
export class BudgetComponent implements OnInit, OnDestroy {
  private budgetDataService = inject(BudgetDataService);
  private router = inject(Router);
  private themeService = inject(ThemeService);
  private authService = inject(AuthService);

  // Use computed signals for reactive data
  budgets = computed(() => this.budgetDataService.budgets());
  categories = computed(() => this.budgetDataService.categories());
  totalBudget = computed(() => this.budgetDataService.totalBudget());

  // Auth signals
  isAuthenticated = computed(() => this.authService.isAuthenticated());
  currentUser = computed(() => this.authService.currentUser());
  totalSpent = computed(() => this.budgetDataService.totalSpent());
  totalRemaining = computed(() => this.budgetDataService.totalRemaining());
  percentSpent = computed(() => this.budgetDataService.percentSpent());
  percentRemaining = computed(() => this.budgetDataService.percentRemaining());
  isLoading = computed(() => this.budgetDataService.isLoading());

  isDarkMode: boolean = false;
  private destroy$ = new Subject<void>();

  ngOnInit(): void {
    // Subscribe to theme changes
    this.themeService.isDarkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isDark => {
        this.isDarkMode = isDark;
      });

    // Budget data is now handled automatically by signals
    // No need for manual subscriptions - the computed signals will update automatically
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  createNewBudget(): void {
    this.router.navigate(['/new-budget']);
  }

  viewBudgetDetails(categoryName: string): void {
    // Navigate to budget details page (to be implemented)
    console.log(`Viewing details for ${categoryName}`);
  }

  editBudget(budgetId: string): void {
    if (budgetId) {
      this.router.navigate(['/edit-budget', budgetId]);
    }
  }
}
