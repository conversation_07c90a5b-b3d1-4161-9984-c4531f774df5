import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProgressCircleComponent } from './progress-circle/progress-circle.component';
import { TrendChartComponent } from './trend-chart/trend-chart.component';
import { ExpenseListComponent } from './expense-list/expense-list.component';
import { AuthWarningComponent } from './auth-warning/auth-warning.component';

@NgModule({
  imports: [
    CommonModule,
    ProgressCircleComponent,
    TrendChartComponent,
    ExpenseListComponent,
    AuthWarningComponent
  ],
  exports: [
    ProgressCircleComponent,
    TrendChartComponent,
    ExpenseListComponent,
    AuthWarningComponent
  ]
})
export class SharedModule { }