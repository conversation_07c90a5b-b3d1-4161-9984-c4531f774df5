import { Component, inject, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LoyaltyCardDataService } from '../core/loyalty-cards/loyalty-card-data.service';
import { FinancialSavingsDataService } from '../core/financial-savings/financial-savings-data.service';
import { CARD_COLORS } from '../interfaces/loyalty-card';
import { SavingPeriod, SavingFrequency } from '../interfaces/financial-savings';

/**
 * Example component demonstrating how to use the new signal-based services
 * for loyalty cards and financial savings with auth integration
 */
@Component({
  selector: 'app-signal-based-usage',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="signal-example-container">
      <h2>Signal-Based Services Example</h2>
      
      <!-- Auth Status -->
      <div class="auth-status">
        <h3>Authentication Status</h3>
        <p>Current User: {{ loyaltyCardDataService.currentUserEmail() || 'Not authenticated' }}</p>
      </div>

      <!-- Loyalty Cards Section -->
      <div class="loyalty-cards-section">
        <h3>Loyalty Cards ({{ loyaltyCardDataService.totalCards() }})</h3>
        <p>Loading: {{ loyaltyCardDataService.isLoading() }}</p>
        
        <button (click)="addSampleLoyaltyCard()" [disabled]="loyaltyCardDataService.isLoading()">
          Add Sample Loyalty Card
        </button>
        
        <div class="cards-list">
          <div *ngFor="let card of loyaltyCardDataService.cards()" class="card-item">
            <div class="card-info">
              <h4>{{ card.name }}</h4>
              <p>Card Number: {{ card.cardNumber }}</p>
              <p>Color: <span [style.background-color]="card.color" class="color-preview"></span></p>
              <p>Created: {{ card.createdAt | date }}</p>
              <p>User: {{ card.userId }}</p>
            </div>
            <button (click)="deleteLoyaltyCard(card.id)" class="delete-btn">Delete</button>
          </div>
        </div>
      </div>

      <!-- Financial Savings Section -->
      <div class="savings-section">
        <h3>Saving Plans ({{ financialSavingsDataService.totalPlans() }})</h3>
        <p>Loading: {{ financialSavingsDataService.isLoading() }}</p>
        
        <div class="savings-summary">
          <p>Total Objective: {{ financialSavingsDataService.totalObjective() }} TND</p>
          <p>Total Current: {{ financialSavingsDataService.totalCurrent() }} TND</p>
          <p>Total Remaining: {{ financialSavingsDataService.totalRemaining() }} TND</p>
          <p>Overall Progress: {{ financialSavingsDataService.overallProgress() | number:'1.1-1' }}%</p>
        </div>
        
        <button (click)="addSampleSavingPlan()" [disabled]="financialSavingsDataService.isLoading()">
          Add Sample Saving Plan
        </button>
        
        <div class="plans-list">
          <div *ngFor="let plan of financialSavingsDataService.plans()" class="plan-item">
            <div class="plan-info">
              <h4>{{ plan.name }}</h4>
              <p>Objective: {{ plan.objective }} TND</p>
              <p>Current: {{ plan.current }} TND</p>
              <p>Progress: {{ plan.percentComplete | number:'1.1-1' }}%</p>
              <p>Period: {{ plan.period }}</p>
              <p>Frequency: {{ plan.frequency }}</p>
              <p>User: {{ plan.userId }}</p>
            </div>
            <div class="plan-actions">
              <button (click)="addFunds(plan.id, 100)" class="add-funds-btn">Add 100 TND</button>
              <button (click)="deleteSavingPlan(plan.id)" class="delete-btn">Delete</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Computed Values Example -->
      <div class="computed-section">
        <h3>Computed Values Example</h3>
        <p>Total Items: {{ totalItems() }}</p>
        <p>Has Data: {{ hasAnyData() }}</p>
        <p>Summary: {{ dataSummary() }}</p>
      </div>
    </div>
  `,
  styles: [`
    .signal-example-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .auth-status, .loyalty-cards-section, .savings-section, .computed-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
    }

    .cards-list, .plans-list {
      display: grid;
      gap: 15px;
      margin-top: 15px;
    }

    .card-item, .plan-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      border: 1px solid #eee;
      border-radius: 6px;
      background: #f9f9f9;
    }

    .card-info, .plan-info {
      flex: 1;
    }

    .plan-actions {
      display: flex;
      gap: 10px;
    }

    .color-preview {
      display: inline-block;
      width: 20px;
      height: 20px;
      border-radius: 3px;
      vertical-align: middle;
    }

    .savings-summary {
      background: #f0f8ff;
      padding: 15px;
      border-radius: 6px;
      margin: 15px 0;
    }

    button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }

    button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .delete-btn {
      background: #ff4444;
      color: white;
    }

    .add-funds-btn {
      background: #4CAF50;
      color: white;
    }

    button:not(.delete-btn):not(.add-funds-btn) {
      background: #2196F3;
      color: white;
    }

    h2, h3 {
      color: #333;
      margin-bottom: 15px;
    }

    h4 {
      margin: 0 0 8px 0;
      color: #555;
    }

    p {
      margin: 4px 0;
      color: #666;
    }
  `]
})
export class SignalBasedUsageComponent {
  // Inject the signal-based services
  loyaltyCardDataService = inject(LoyaltyCardDataService);
  financialSavingsDataService = inject(FinancialSavingsDataService);

  // Example computed signals that combine data from both services
  totalItems = computed(() => 
    this.loyaltyCardDataService.totalCards() + this.financialSavingsDataService.totalPlans()
  );

  hasAnyData = computed(() => 
    this.loyaltyCardDataService.totalCards() > 0 || this.financialSavingsDataService.totalPlans() > 0
  );

  dataSummary = computed(() => {
    const cards = this.loyaltyCardDataService.totalCards();
    const plans = this.financialSavingsDataService.totalPlans();
    const totalSavings = this.financialSavingsDataService.totalCurrent();
    
    return `You have ${cards} loyalty cards, ${plans} saving plans, and ${totalSavings} TND saved.`;
  });

  addSampleLoyaltyCard(): void {
    const colors = this.loyaltyCardDataService.getAvailableColors();
    const randomColor = colors[Math.floor(Math.random() * colors.length)];
    const timestamp = Date.now();
    
    this.loyaltyCardDataService.addLoyaltyCard({
      name: `Sample Card ${timestamp}`,
      cardNumber: `CARD-${timestamp}`,
      color: randomColor
    }).subscribe(card => {
      console.log('Added loyalty card:', card);
    });
  }

  deleteLoyaltyCard(cardId: string): void {
    this.loyaltyCardDataService.deleteLoyaltyCard(cardId).subscribe(success => {
      console.log('Delete loyalty card result:', success);
    });
  }

  addSampleSavingPlan(): void {
    const timestamp = Date.now();
    
    this.financialSavingsDataService.addSavingPlan({
      name: `Sample Plan ${timestamp}`,
      objective: 1000,
      current: 100,
      period: SavingPeriod.SixMonths,
      frequency: SavingFrequency.Monthly,
      startDate: new Date()
    }).subscribe(plan => {
      console.log('Added saving plan:', plan);
    });
  }

  addFunds(planId: string, amount: number): void {
    this.financialSavingsDataService.addFundsToSavingPlan(planId, amount).subscribe(updatedPlan => {
      console.log('Added funds to plan:', updatedPlan);
    });
  }

  deleteSavingPlan(planId: string): void {
    this.financialSavingsDataService.deleteSavingPlan(planId).subscribe(success => {
      console.log('Delete saving plan result:', success);
    });
  }
}
