import { Injectable, inject } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { LoyaltyCard } from '../models/loyalty-card.model';
import { AppStorageService } from './app-storage.service';
import { LoyaltyCardDataService } from '../core/loyalty-cards/loyalty-card-data.service';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class LoyaltyCardService {
  private readonly LOYALTY_CARDS = 'loyalty_cards';
  private loyaltyCards: LoyaltyCard[] = [];
  private loyaltyCardsSubject = new BehaviorSubject<LoyaltyCard[]>([]);
  // Flag to determine if we're in development mode
  private readonly isDevelopmentMode = true; // Set to true for development mode

  // Inject the new signal-based data service
  private loyaltyCardDataService = inject(LoyaltyCardDataService);

  constructor(private appStorage: AppStorageService) {
    // Initialize the storage key in AppStorageService
    this.initializeStorage();
    // Load loyalty cards
    this.loadLoyaltyCards();
  }

  private initializeStorage(): void {
    // Check if the LOYALTY_CARDS key exists in the memoryStorage
    if (!this.appStorage['memoryStorage'].has(this.LOYALTY_CARDS)) {
      // Initialize with empty array
      this.appStorage['memoryStorage'].set(this.LOYALTY_CARDS, []);
    }
  }

  private loadLoyaltyCards(): void {
    try {
      // In development mode, we don't want to load from localStorage to ensure data is cleared on reload
      if (this.isDevelopmentMode) {
        // Only use memory storage in development mode
        this.loyaltyCards = this.appStorage['memoryStorage'].get(this.LOYALTY_CARDS) || [];
      } else {
        // In production mode, try to get loyalty cards from localStorage first for persistence
        const savedCards = localStorage.getItem(this.LOYALTY_CARDS);
        if (savedCards) {
          const parsedCards = JSON.parse(savedCards);
          // Convert string dates back to Date objects
          this.loyaltyCards = parsedCards.map((card: any) => ({
            ...card,
            createdAt: new Date(card.createdAt)
          }));
        } else {
          // If not in localStorage, try to get from AppStorageService
          this.loyaltyCards = this.appStorage['memoryStorage'].get(this.LOYALTY_CARDS) || [];
        }
      }

      // Update the subject
      this.loyaltyCardsSubject.next([...this.loyaltyCards]);
    } catch (e) {
      console.error('Error loading loyalty cards:', e);
      this.loyaltyCards = [];
      this.loyaltyCardsSubject.next([]);
    }
  }

  private saveLoyaltyCards(): void {
    try {
      // Save to AppStorageService
      this.appStorage['memoryStorage'].set(this.LOYALTY_CARDS, this.loyaltyCards);

      // Only save to localStorage in production mode
      if (!this.isDevelopmentMode) {
        localStorage.setItem(this.LOYALTY_CARDS, JSON.stringify(this.loyaltyCards));
      }

      // Update the subject
      this.loyaltyCardsSubject.next([...this.loyaltyCards]);
    } catch (e) {
      console.error('Error saving loyalty cards:', e);
    }
  }

  getLoyaltyCards(): Observable<LoyaltyCard[]> {
    // Create an Observable from the signal-based service data
    const signalCards = this.loyaltyCardDataService.cards();

    if (signalCards.length > 0) {
      // Convert signal data to legacy format and return as Observable
      const legacyCards = signalCards.map(card => ({
        id: card.id,
        name: card.name,
        cardNumber: card.cardNumber,
        color: card.color,
        createdAt: card.createdAt,
        barcodeImage: card.barcodeImage
      }));
      return of(legacyCards);
    }

    // Fallback to legacy observable
    return this.loyaltyCardsSubject.asObservable();
  }

  addLoyaltyCard(card: Omit<LoyaltyCard, 'id' | 'createdAt'>): LoyaltyCard {
    // Use the signal-based service for new cards
    this.loyaltyCardDataService.addLoyaltyCard(card).subscribe(newCard => {
      if (newCard) {
        // Convert to legacy format for backward compatibility
        const legacyCard: LoyaltyCard = {
          id: newCard.id,
          name: newCard.name,
          cardNumber: newCard.cardNumber,
          color: newCard.color,
          createdAt: newCard.createdAt,
          barcodeImage: newCard.barcodeImage
        };

        // Update legacy storage for components that still use it
        this.loyaltyCards.unshift(legacyCard);
        this.saveLoyaltyCards();
      }
    });

    // Return a temporary card for immediate use
    const tempCard: LoyaltyCard = {
      ...card,
      id: Date.now().toString(),
      createdAt: new Date()
    };
    return tempCard;
  }

  deleteLoyaltyCard(id: string): void {
    // Use the signal-based service
    this.loyaltyCardDataService.deleteLoyaltyCard(id).subscribe(success => {
      if (success) {
        // Update legacy storage
        this.loyaltyCards = this.loyaltyCards.filter(card => card.id !== id);
        this.saveLoyaltyCards();
      }
    });
  }

  getLoyaltyCardById(id: string): LoyaltyCard | undefined {
    // Try signal-based service first
    const signalCard = this.loyaltyCardDataService.getLoyaltyCardById(id);
    if (signalCard) {
      return {
        id: signalCard.id,
        name: signalCard.name,
        cardNumber: signalCard.cardNumber,
        color: signalCard.color,
        createdAt: signalCard.createdAt,
        barcodeImage: signalCard.barcodeImage
      };
    }

    // Fallback to legacy storage
    return this.loyaltyCards.find(card => card.id === id);
  }

  updateCardBarcode(id: string, barcodeImage: string): void {
    // Use the signal-based service
    this.loyaltyCardDataService.updateCardBarcode(id, barcodeImage).subscribe(success => {
      if (success) {
        // Update legacy storage
        const cardIndex = this.loyaltyCards.findIndex(card => card.id === id);
        if (cardIndex !== -1) {
          this.loyaltyCards[cardIndex] = {
            ...this.loyaltyCards[cardIndex],
            barcodeImage
          };
          this.saveLoyaltyCards();
        }
      }
    });
  }
}
