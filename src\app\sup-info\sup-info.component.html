<app-security-alerts></app-security-alerts>

<div class="container">
  <div class="left-section">
    <h1>Complete Your Profile</h1>
    <p>Please provide additional information to complete your account setup</p>

    <form (ngSubmit)="onSubmit()" #supInfoForm="ngForm">
      <!-- Birthday Field -->
      <div class="form-group">
        <label for="birthday">Birthday</label>
        <input
          type="date"
          id="birthday"
          name="birthday"
          [(ngModel)]="supInfoData.birthday"
          required
          [max]="getMaxDate()"
          placeholder="Select your birthday">
      </div>

      <!-- Gender Field -->
      <div class="form-group">
        <label for="gender">Gender</label>
        <select
          id="gender"
          name="gender"
          [(ngModel)]="supInfoData.gender"
          required>
          <option value="male">Male</option>
          <option value="female">Female</option>
          <option value="other">Other</option>
        </select>
      </div>

      <!-- Phone Number Field -->
      <div class="form-group">
        <label for="phoneNumber">Phone Number</label>
        <input
          type="tel"
          id="phoneNumber"
          name="phoneNumber"
          [(ngModel)]="supInfoData.phoneNumber"
          (input)="onPhoneInput($event)"
          (keydown)="onPhoneKeydown($event)"
          required
          placeholder="+216 12 345 678"
          minlength="12">
      </div>

      <div class="button-group">
        <button type="submit" class="create-account-btn" [disabled]="!supInfoForm.valid">
          COMPLETE PROFILE
        </button>
        <button type="button" class="skip-btn" (click)="onSkip()">
          SKIP
        </button>
      </div>
    </form>

    <p class="success-message" *ngIf="successMessage">{{ successMessage }}</p>
    <p class="error-message" *ngIf="errorMessage">{{ errorMessage }}</p>

    <footer>© 2023 Receeto ALL RIGHTS RESERVED.</footer>
  </div>

  <div class="right-section">
    <div class="receeto-logo"></div>
    <div class="bottom-nav">
      <a href="https://receeto.com" target="_blank" class="nav-link">Website</a>
      <a href="#" class="nav-link">Documentation</a>
      <a href="#" class="nav-link">Terms of Use</a>
      <a href="#" class="nav-link">Blog</a>
    </div>
  </div>
</div>
