export interface AuthUser {
  full_name: string;
  email: string;
  avatar: string;
  is_initialized: boolean;
  role?: string;
  // Avatar-specific properties for proper user isolation
  hasCustomAvatar?: boolean;
  avatarPositionY?: number;
  // Sup-info properties
  supInfo?: {
    birthday?: string;
    gender?: 'male' | 'female' | 'other';
    phoneNumber?: string;
  };
}

// Type guard for AuthUser
export function isAuthUser(obj: any): obj is AuthUser {
  return obj && typeof obj === 'object' && 'full_name' in obj && 'email' in obj;
}
