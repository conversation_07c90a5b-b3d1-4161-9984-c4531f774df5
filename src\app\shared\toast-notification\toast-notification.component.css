/* Toast Container */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 400px;
  width: 100%;
  pointer-events: none;
}

/* Individual Toast Message */
.toast-message {
  display: flex;
  align-items: flex-start;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  margin-bottom: 12px;
  padding: 16px;
  border-left: 4px solid;
  pointer-events: auto;
  animation: slideInRight 0.3s ease-out;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.toast-message:hover {
  transform: translateX(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Toast Types */
.toast-success {
  border-left-color: #10b981;
  background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
}

.toast-error {
  border-left-color: #ef4444;
  background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
}

.toast-warning {
  border-left-color: #f59e0b;
  background: linear-gradient(135deg, #ffffff 0%, #fffbeb 100%);
}

.toast-info {
  border-left-color: #6b48ff;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

/* Toast Icon */
.toast-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  margin-right: 12px;
  margin-top: 2px;
}

.toast-success .toast-icon i {
  color: #10b981;
  font-size: 20px;
}

.toast-error .toast-icon i {
  color: #ef4444;
  font-size: 20px;
}

.toast-warning .toast-icon i {
  color: #f59e0b;
  font-size: 20px;
}

.toast-info .toast-icon i {
  color: #6b48ff;
  font-size: 20px;
}

/* Toast Content */
.toast-content {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-weight: 600;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 4px;
  color: #1f2937;
}

.toast-message-text {
  font-size: 13px;
  line-height: 1.4;
  color: #6b7280;
}

/* Close Button */
.toast-close-btn {
  flex-shrink: 0;
  background: none;
  border: none;
  padding: 4px;
  margin-left: 8px;
  margin-top: -2px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  color: #9ca3af;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-close-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #6b7280;
}

.toast-close-btn i {
  font-size: 12px;
}

/* Dark Mode Styles */
.toast-container.dark-mode .toast-message {
  background: #1f2937;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.toast-container.dark-mode .toast-success {
  background: linear-gradient(135deg, #1f2937 0%, #064e3b 100%);
}

.toast-container.dark-mode .toast-error {
  background: linear-gradient(135deg, #1f2937 0%, #7f1d1d 100%);
}

.toast-container.dark-mode .toast-warning {
  background: linear-gradient(135deg, #1f2937 0%, #78350f 100%);
}

.toast-container.dark-mode .toast-info {
  background: linear-gradient(135deg, #1f2937 0%, #312e81 100%);
}

.toast-container.dark-mode .toast-title {
  color: #f9fafb;
}

.toast-container.dark-mode .toast-message-text {
  color: #d1d5db;
}

.toast-container.dark-mode .toast-close-btn {
  color: #9ca3af;
}

.toast-container.dark-mode .toast-close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #f3f4f6;
}

/* Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .toast-message {
    margin-bottom: 8px;
    padding: 12px;
  }
  
  .toast-title {
    font-size: 13px;
  }
  
  .toast-message-text {
    font-size: 12px;
  }
}
