<app-top-navbar></app-top-navbar>
<app-sidebar></app-sidebar>

<!-- Authentication Warning -->
<app-auth-warning></app-auth-warning>

<div class="budget-container" [class.dark-mode]="isDarkMode" *ngIf="isAuthenticated()">
  <div class="budget-header">
    <h1>Budgets</h1>
  </div>

  <div class="budget-overview">
    <h2>How much can I spend to meet my budget?</h2>

    <div class="budget-progress">
      <div class="budget-progress-item total-budget-item">
        <div class="budget-label">Monthly budget</div>
        <div class="budget-value">{{ totalBudget().toFixed(2) }} TND</div>
        <div class="progress-bar">
          <div class="total-progress-fill"
               [style.width.%]="percentRemaining()"></div>
        </div>
      </div>

      <div class="budget-progress-item total-budget-item">
        <div class="budget-label">Left to spend</div>
        <div class="budget-value">{{ totalRemaining().toFixed(2) }} TND</div>
        <div class="progress-bar">
          <div class="progress-fill"
               [style.width.%]="percentSpent()"
               [ngClass]="{
                 'green-progress': percentSpent() < 80,
                 'red-progress': percentSpent() >= 80
               }"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Budget List Section -->
  <div class="budget-list" *ngIf="budgets().length > 0">
    <h2>Your Budgets</h2>
    <div class="budget-items">
      <div *ngFor="let budget of budgets(); let i = index" class="budget-item" (click)="editBudget(budget.id)">
        <div class="budget-item-header">
          <h3>{{ budget.name || 'Budget Number ' + (i + 1) }}</h3>
          <div class="budget-item-period">
            {{ budget.period }}
          </div>
        </div>
        <div class="budget-item-details">
          <div class="budget-item-amount">{{ budget.amount.toFixed(2) }} TND</div>
          <div class="budget-item-category">{{ budget.category }}</div>
        </div>
        <div class="budget-item-progress">
          <div class="progress-bar">
            <div class="progress-fill"
                 [style.width.%]="budget.percentSpent || 0"
                 [ngClass]="{
                   'green-progress': (budget.percentSpent || 0) < 80,
                   'red-progress': (budget.percentSpent || 0) >= 80
                 }"></div>
          </div>
          <div class="budget-item-stats">
            <span>{{ (budget.spent || 0).toFixed(2) }} TND spent</span>
            <span>{{ (budget.remaining || budget.amount).toFixed(2) }} TND left</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State Message -->
  <div class="empty-budget-message" *ngIf="budgets().length === 0">
    <div class="message-content">
      <i class="fas fa-wallet"></i>
      <h3>No Budgets Yet</h3>
      <p>Create your first budget to start tracking your expenses.</p>
    </div>
  </div>

  <div class="create-budget-container">
    <button class="create-budget-btn" (click)="createNewBudget()">Create A New Budget</button>
  </div>
</div>
