<app-top-navbar></app-top-navbar>
<app-sidebar></app-sidebar>

<!-- Authentication Warning -->
<app-auth-warning></app-auth-warning>

<div class="edit-savings-container" [class.dark-mode]="isDarkMode" *ngIf="isAuthenticated()">
  <div class="edit-savings-header">
    <h1>{{ isEditing ? 'Edit Financial Savings' : 'New Financial Savings' }}</h1>
  </div>

  <div class="savings-form" [class.dark-mode]="isDarkMode" *ngIf="!isLoading && !notFound">
    <!-- What are you saving for? -->
    <div class="form-section saving-name-section" [class.dark-mode]="isDarkMode">
      <h2>{{ isEditing ? 'What do you want to edit?' : 'What are you saving for?' }}</h2>
      <div class="form-group">
        <input type="text" [(ngModel)]="savingPlan.name" placeholder="Saving Plan Name" class="form-control" required #nameInput="ngModel"
               [class.invalid-input]="(nameInput.invalid && (nameInput.dirty || nameInput.touched)) || (formSubmitted && nameInput.invalid)">
        <div class="validation-error" *ngIf="(nameInput.invalid && (nameInput.dirty || nameInput.touched)) || (formSubmitted && nameInput.invalid)">
          Saving Plan Name is required
        </div>
        <div class="form-hint">A descriptive name makes a plan memorable</div>
      </div>
    </div>

    <!-- How much would you like to start with? -->
    <div class="form-section saving-amount-section" [class.dark-mode]="isDarkMode">
      <h2>How much would you like to start with?</h2>
      <div class="amount-options">
        <div class="amount-option"
             *ngFor="let option of savingAmounts"
             [class.selected]="selectedAmount === option.amount && isCustomAmount === option.isCustom"
             (click)="selectAmount(option.amount, option.isCustom)">
          <ng-container *ngIf="!option.isCustom">
            {{ option.amount }} TND
          </ng-container>
          <ng-container *ngIf="option.isCustom">
            Other
          </ng-container>
        </div>
      </div>

      <!-- Custom amount input -->
      <div class="custom-amount-input" *ngIf="isCustomAmount">
        <input type="number"
               [(ngModel)]="customAmount"
               (ngModelChange)="onCustomAmountChange($event)"
               placeholder="Enter amount"
               class="form-control"
               (keyup.enter)="calculateTotal()">
        <span class="currency">TND</span>
      </div>
    </div>

    <!-- Duration Savings Section -->
    <div class="form-section saving-duration-section" [class.dark-mode]="isDarkMode" [style]="{'background-color': isDarkMode ? '#2a2a2a' : '#ffffff', 'border': isDarkMode ? '1px solid rgba(107, 72, 255, 0.2)' : 'none', 'pointer-events': 'auto'}">
      <h2>Duration Savings</h2>
      <div class="duration-inputs-container">
        <!-- Amount Input -->
        <div class="duration-amount-input">
          <label class="form-label">Enter Amount:</label>
          <div class="amount-input-group" [style]="{'display': 'flex', 'border': '1px solid rgba(171, 85, 247, 0.2)', 'border-radius': '12px', 'overflow': 'hidden', 'background-color': '#333'}">
            <input type="number"
                   [(ngModel)]="durationAmount"
                   (ngModelChange)="calculateDurationTotal()"
                   placeholder="Enter amount"
                   class="form-control"
                   [style]="{'border': 'none', 'background-color': 'transparent', 'color': '#fff', 'box-shadow': 'none', 'flex': '1'}"
                   [min]="0"
                   (focus)="onDurationFocus()"
                   (blur)="onDurationBlur()">
            <span class="currency" [style]="{'border': 'none', 'background-color': 'transparent', 'color': '#888', 'padding': '8px'}">TND</span>
          </div>
          <div class="form-hint">Amount to save per duration</div>
        </div>

        <!-- Duration Input -->
        <div class="duration-period-input">
          <label class="form-label">Duration:</label>
          <div class="duration-input-group" [style]="{'display': 'flex', 'border': '1px solid rgba(171, 85, 247, 0.2)', 'border-radius': '12px', 'overflow': 'hidden', 'background-color': '#333'}">
            <input type="number"
                   [(ngModel)]="durationValue"
                   (ngModelChange)="calculateDurationTotal()"
                   placeholder="Enter duration"
                   class="form-control"
                   [style]="{'border': 'none', 'background-color': 'transparent', 'color': '#fff', 'box-shadow': 'none', 'flex': '1'}"
                   [min]="1"
                   (focus)="onDurationValueFocus()"
                   (blur)="onDurationValueBlur()">
            <select [(ngModel)]="durationType"
                     (ngModelChange)="calculateDurationTotal()"
                     class="duration-select"
                     [style]="{'border': 'none', 'background-color': '#444', 'color': '#ffffff', 'box-shadow': 'none', 'width': '120px', 'padding': '8px', 'border-radius': '6px', 'margin': '5px', 'font-weight': 'bold'}">
              <option value="weeks">Weeks</option>
              <option value="months">Months</option>
              <option value="years">Years</option>
            </select>
          </div>
          <!-- Duration Conversion Display -->
          <div class="duration-conversion" *ngIf="getDurationConversion()" [style]="{'margin-top': '8px', 'font-size': '13px', 'color': '#888', 'font-style': 'italic'}">
            {{ getDurationConversion() }}
          </div>
        </div>
      </div>

      <!-- Total Calculation -->
      <div class="duration-total-calculation" *ngIf="durationTotal > 0">
        <div class="calculation-detail">
          <span class="calculation-label">Duration Savings:</span>
          <span class="calculation-item">{{ durationAmount }} TND</span> ×
          <span class="calculation-item">{{ durationValue }} {{ durationType }}</span>
          <span class="calculation-label">+</span>
          <span class="calculation-item">{{ selectedAmount }} TND</span>
          <span class="calculation-label">(initial amount)</span>
        </div>
        <div class="total-amount">
          Total: {{ durationTotal }} TND
        </div>
      </div>
    </div>



    <!-- Action buttons -->
    <div class="form-actions">
      <button class="cancel-btn" (click)="cancel()">Cancel</button>
      <button class="delete-btn" *ngIf="isEditing" (click)="deleteSavingPlan()">Delete</button>
      <button class="save-btn" (click)="showCongratulations()">{{ isEditing ? 'Save edit' : 'Done' }}</button>
    </div>
  </div>

  <!-- Loading state -->
  <div class="loading-state" *ngIf="isLoading">
    <div class="spinner"></div>
    <p>Loading...</p>
  </div>

  <!-- Not found state -->
  <div class="not-found-state" *ngIf="notFound">
    <div class="message-content">
      <i class="fas fa-exclamation-circle"></i>
      <h3>Saving Plan Not Found</h3>
      <p>The saving plan you're looking for doesn't exist.</p>
      <button class="back-btn" (click)="cancel()">Back to Saving Plans</button>
    </div>
  </div>
</div>
