import { Component, OnInit, HostListener, inject, computed, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { TrendChartComponent } from '../shared/trend-chart/trend-chart.component';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { ProgressCircleComponent } from '../shared/progress-circle/progress-circle.component';
import { ExpenseListComponent } from '../shared/expense-list/expense-list.component';
import { AuthWarningComponent } from '../shared/auth-warning/auth-warning.component';
import { SidebarService } from '../sidebar/sidebar.service';
import { AuthService } from '../core/auth/auth.service';
import { ShopperAnalyticsDataService } from '../core/shopper-analytics/shopper-analytics-data.service';
import {
  ShopperAnalyticsData,
  SavingsGoalProgressData,
  BudgetSpentProgressData,
  SpendingSectionData,
  StatsGridData,
  CategoryExpensesData,
  ProductExpensesData
} from '../interfaces/shopper-analytics';

interface ExpenseData {
  budgetProgress: {
    savingsGoalPercentage: number;
    budgetSpentPercentage: number;
  };
  monthlySpending: {
    amount: number;
    percentage: number;
    percentageChange: number;
    trendData: number[];
  };
  expensesByCategory: Array<{
    name: string;
    amount: number;
    percentage: number;
  }>;
  expensesByProduct: Array<{
    name: string;
    amount: number;
    percentage: number;
  }>;
  summaryValue: string;
}

@Component({
  selector: 'app-shopper-analytics',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TrendChartComponent,
    TopNavbarComponent,
    SidebarComponent,
    ProgressCircleComponent,
    ExpenseListComponent,
    AuthWarningComponent
  ],
  templateUrl: './shopper-analytics.component.html',
  styleUrls: ['./shopper-analytics.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ShopperAnalyticsComponent implements OnInit {
  isMobile = window.innerWidth <= 768;
  isMobileMenuOpen = false;

  // Inject services
  private sidebarService = inject(SidebarService);
  private authService = inject(AuthService);
  private analyticsDataService = inject(ShopperAnalyticsDataService);

  // Signal-based reactive data
  isAuthenticated = computed(() => this.authService.isAuthenticated());
  currentUser = computed(() => this.authService.currentUser());
  analytics = computed(() => this.analyticsDataService.analytics());
  savingsGoalProgress = computed(() => this.analyticsDataService.savingsGoalProgress());
  budgetSpentProgress = computed(() => this.analyticsDataService.budgetSpentProgress());
  spendingSection = computed(() => this.analyticsDataService.spendingSection());
  statsGrid = computed(() => this.analyticsDataService.statsGrid());
  categoryExpenses = computed(() => this.analyticsDataService.categoryExpenses());
  productExpenses = computed(() => this.analyticsDataService.productExpenses());
  isLoading = computed(() => this.analyticsDataService.isLoading());
  error = computed(() => this.analyticsDataService.error());

  ngOnInit() {
    console.log('Shopper Analytics component initialized with auth signals');
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.isMobile = window.innerWidth <= 768;
  }

  toggleSidebar() {
    this.sidebarService.toggleSidebar();
  }

  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  closeMobileMenu() {
    this.isMobileMenuOpen = false;
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    const navbarRight = document.querySelector('.navbar-right');
    const avatarContainer = document.querySelector('.avatar-container');
    
    if (navbarRight && !navbarRight.contains(target) && !avatarContainer?.contains(target)) {
      this.closeMobileMenu();
    }
  }

  // Computed analytics data from auth signals
  analyticsData = computed(() => {
    const savingsProgress = this.savingsGoalProgress();
    const budgetProgress = this.budgetSpentProgress();
    const spending = this.spendingSection();
    const stats = this.statsGrid();
    const categories = this.categoryExpenses();
    const products = this.productExpenses();

    if (!savingsProgress || !budgetProgress || !spending || !categories || !products) {
      // Return default data if not loaded yet
      return {
        budgetProgress: {
          savingsGoalPercentage: 0,
          budgetSpentPercentage: 0
        },
        monthlySpending: {
          amount: 0,
          percentage: 0,
          percentageChange: 0,
          trendData: [0, 0, 0, 0, 0, 0, 0]
        },
        expensesByCategory: [],
        expensesByProduct: [],
        summaryValue: '0 + 0'
      };
    }

    return {
      budgetProgress: {
        savingsGoalPercentage: savingsProgress.percentage,
        budgetSpentPercentage: budgetProgress.percentage
      },
      monthlySpending: {
        amount: spending.monthlySpending.amount,
        percentage: spending.monthlySpending.percentage,
        percentageChange: spending.monthlySpending.percentageChange,
        trendData: spending.trendData
      },
      expensesByCategory: categories.categories.map(cat => ({
        name: cat.name,
        amount: cat.amount,
        percentage: cat.percentage
      })),
      expensesByProduct: products.products.map(prod => ({
        name: prod.name,
        amount: prod.amount,
        percentage: prod.percentage
      })),
      summaryValue: `${categories.totalAmount} + ${products.totalAmount}`
    };
  });

  // Helper methods for accessing auth signal data in templates

  // Get savings goal progress percentage
  getSavingsGoalPercentage(): number {
    const data = this.savingsGoalProgress();
    return data ? data.percentage : 0;
  }

  // Get budget spent progress percentage
  getBudgetSpentPercentage(): number {
    const data = this.budgetSpentProgress();
    return data ? data.percentage : 0;
  }

  // Get monthly spending amount
  getMonthlySpendingAmount(): number {
    const data = this.spendingSection();
    return data ? data.monthlySpending.amount : 0;
  }

  // Get monthly spending percentage change
  getMonthlySpendingChange(): number {
    const data = this.spendingSection();
    return data ? data.monthlySpending.percentageChange : 0;
  }

  // Get trend data for chart
  getTrendData(): number[] {
    const data = this.spendingSection();
    return data ? data.trendData : [0, 0, 0, 0, 0, 0, 0];
  }

  // Get top category name
  getTopCategoryName(): string {
    const data = this.statsGrid();
    return data ? data.topCategory.name : 'No Data';
  }

  // Get top category percentage
  getTopCategoryPercentage(): number {
    const data = this.statsGrid();
    return data ? data.topCategory.percentage : 0;
  }

  // Get monthly percentage for stats
  getMonthlyPercentage(): number {
    const data = this.statsGrid();
    return data ? data.monthlyPercentage : 0;
  }

  // Get category expenses list
  getCategoryExpenses() {
    const data = this.categoryExpenses();
    return data ? data.categories : [];
  }

  // Get product expenses list
  getProductExpenses() {
    const data = this.productExpenses();
    return data ? data.products : [];
  }

  // Refresh analytics data (signals handle automatic updates)
  refreshData(): void {
    console.log('Refresh requested - signals handle automatic updates, forcing card auth refresh...');
    this.analyticsDataService.refreshAnalyticsData().subscribe(success => {
      if (success) {
        console.log('Card auth data refreshed - analytics will update automatically via signals');
      }
    });
  }

  // Test data methods (for development)
  addTestData(): void {
    this.analyticsDataService.addTestAnalyticsData();
  }

  clearTestData(): void {
    this.analyticsDataService.clearTestAnalyticsData();
  }

  // Helper method to format amounts - only show decimals when needed
  formatAmount(amount: number): string {
    if (amount % 1 === 0) {
      // If the number is a whole number, don't show decimals
      return amount.toString();
    } else {
      // If the number has decimals, show up to 2 decimal places
      return amount.toFixed(2);
    }
  }

  // Get formatted monthly spending amount
  getFormattedMonthlySpendingAmount(): string {
    return this.formatAmount(this.getMonthlySpendingAmount());
  }

  // Get formatted top category amount
  getFormattedTopCategoryAmount(): string {
    const data = this.statsGrid();
    return data ? this.formatAmount(data.topCategory.amount) : '0';
  }

  // Get formatted top product amount
  getFormattedTopProductAmount(): string {
    const products = this.getProductExpenses();
    return products.length > 0 ? this.formatAmount(products[0].amount) : '0';
  }

  // Get formatted budget total spent
  getFormattedBudgetTotalSpent(): string {
    const data = this.budgetSpentProgress();
    return data ? this.formatAmount(data.totalSpent) : '0';
  }

  // Get formatted budget total budget
  getFormattedBudgetTotal(): string {
    const data = this.budgetSpentProgress();
    return data ? this.formatAmount(data.totalBudget) : '0';
  }

  // Get formatted savings total current
  getFormattedSavingsTotalCurrent(): string {
    const data = this.savingsGoalProgress();
    return data ? this.formatAmount(data.totalCurrent) : '0';
  }

  // Get formatted savings total goal
  getFormattedSavingsTotalGoal(): string {
    const data = this.savingsGoalProgress();
    return data ? this.formatAmount(data.totalGoal) : '0';
  }
}