/* Reset default styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  .container {
    display: flex;
    height: 100vh;
    font-family: Arial, sans-serif;
  }
  
  .left-section {
    flex: 1;
    padding: 40px;
    background: #f5f5f5;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    align-items: center;
  }
  
  .right-section {
    flex: 1;
    background: linear-gradient(135deg, #6b48ff, #a855f7);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 40px;
    color: white;
    height: 100%;
    min-height: 100vh;
    position: relative;
    border-bottom-left-radius: 50px;
  }
  
  .back-link {
    position: absolute;
    top: 20px;
    left: 20px;
    color: #666;
    text-decoration: none;
    font-size: 0.9rem;
  }
  
  h1 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 10px;
  }
  
  p {
    margin-bottom: 20px;
    color: #666;
    font-size: 0.9rem;
    text-align: center;
    max-width: 400px;
  }
  
  p strong {
    color: #000;
  }
  
  .resend-btn {
    padding: 12px;
    background: #6b48ff;
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    text-transform: uppercase;
    transition: background 0.3s;
    width: 100%;
    max-width: 400px;
  }
  
  .resend-btn:hover {
    background: #5a3de6;
  }
  
  footer {
    margin-top: 20px;
    color: #666;
    font-size: 0.8rem;
  }
  
  .right-section h2 {
    font-size: 3.5rem;
    font-weight: bold;
    letter-spacing: 2px;
    margin: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
  }
  
  .right-section nav {
    display: flex;
    gap: 20px;
    justify-content: flex-start;
    margin-top: auto;
  }
  
  .right-section nav a {
    color: white;
    text-decoration: none;
    font-size: 0.9rem;
    padding: 5px 10px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    transition: background 0.3s;
  }
  
  .right-section nav a:hover {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .button-group {
    display: flex;
    gap: 10px;
    margin: 20px 0;
  }
  
  .skip-btn {
    background: none;
    border: 1px solid #ccc;
    color: #666;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
  }
  
  .skip-btn:hover {
    background-color: #f5f5f5;
    border-color: #999;
    color: #333;
  }