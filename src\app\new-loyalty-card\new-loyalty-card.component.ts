import { Component, ElementRef, OnInit, ViewChild, inject, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { LoyaltyCardService } from '../services/loyalty-card.service';
import { CARD_COLORS } from '../models/loyalty-card.model';
import { ThemeService } from '../services/theme.service';
import { AuthWarningComponent } from '../shared/auth-warning/auth-warning.component';
import { AuthService } from '../core/auth/auth.service';

@Component({
  selector: 'app-new-loyalty-card',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    TopNavbarComponent,
    SidebarComponent,
    AuthWarningComponent
  ],
  templateUrl: './new-loyalty-card.component.html',
  styleUrls: ['./new-loyalty-card.component.scss']
})
export class NewLoyaltyCardComponent implements OnInit {
  // Inject services
  private loyaltyCardService = inject(LoyaltyCardService);
  private router = inject(Router);
  private themeService = inject(ThemeService);
  private authService = inject(AuthService);

  // Auth signals
  isAuthenticated = computed(() => this.authService.isAuthenticated());
  currentUser = computed(() => this.authService.currentUser());

  cardName: string = '';
  cardNumber: string = '';
  selectedColor: string = CARD_COLORS[0]; // Default to first color
  availableColors = CARD_COLORS;
  barcodePreview: string | null = null;
  isDarkMode = false;
  showAutocomplete = false;
  filteredBrands: string[] = [];
  collaboratingBrands = ['Zen', 'Aziza', 'Monoprix', 'Carrefour', 'Strass Sousse Mall', 'Strass Sahloul'];

  @ViewChild('barcodeFileInput') barcodeFileInput!: ElementRef<HTMLInputElement>;

  ngOnInit(): void {
    this.themeService.isDarkMode$.subscribe(isDark => {
      this.isDarkMode = isDark;
    });
  }

  showAllBrands(): void {
    this.showAutocomplete = true;
    this.filteredBrands = this.collaboratingBrands;
  }

  onCardNameInput(event: any): void {
    const value = event.target.value.toLowerCase();
    this.showAutocomplete = true;
    if (!value) {
      this.filteredBrands = this.collaboratingBrands;
    } else {
      this.filteredBrands = this.collaboratingBrands.filter(brand => 
        brand.toLowerCase().includes(value)
      );
    }
  }

  selectBrand(brand: string): void {
    this.cardName = brand;
    this.showAutocomplete = false;
  }

  hideAutocomplete(): void {
    // Small delay to allow click events to fire first
    setTimeout(() => {
      this.showAutocomplete = false;
    }, 200);
  }

  selectColor(color: string): void {
    this.selectedColor = color;
  }

  triggerBarcodeUpload(): void {
    this.barcodeFileInput.nativeElement.click();
  }

  onBarcodeFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];

      // Check if file is an image
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        return;
      }

      // Read the file as data URL
      const reader = new FileReader();
      reader.onload = (e) => {
        this.barcodePreview = e.target?.result as string;
      };

      reader.readAsDataURL(file);
    }
  }

  addCard(): void {
    if (this.isFormValid()) {
      this.loyaltyCardService.addLoyaltyCard({
        name: this.cardName,
        cardNumber: this.cardNumber,
        color: this.selectedColor,
        barcodeImage: this.barcodePreview || undefined
      });

      this.router.navigate(['/loyalty-cards']);
    }
  }

  isFormValid(): boolean {
    return !!this.cardName && !!this.cardNumber && !!this.selectedColor;
  }
}
