<app-top-navbar></app-top-navbar>
<app-sidebar></app-sidebar>

<!-- Authentication Warning -->
<app-auth-warning></app-auth-warning>

<div class="analytics-container" *ngIf="isAuthenticated()">
  <div class="card analytics-card fade-in">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
      <h2 class="dashboard-title">Shopping Analytics</h2>
      <div class="header-actions">
        <div class="date-range">
          <span class="icon">📅</span>
          <span>Current Period</span>
        </div>
        <!-- Test Data Buttons (for development) -->
        <div class="test-buttons" *ngIf="isAuthenticated()">
          <button class="test-btn add-data" (click)="addTestData()" title="Add Test Data">
            📊 Add Test Data
          </button>
          <button class="test-btn clear-data" (click)="clearTestData()" title="Clear Test Data">
            🗑️ Clear Data
          </button>
          <button class="test-btn refresh-data" (click)="refreshData()" title="Refresh Data">
            🔄 Refresh
          </button>
        </div>
      </div>
    </div>



    <!-- Loading State -->
    <div class="loading-container" *ngIf="isAuthenticated() && isLoading()">
      <div class="loading-spinner"></div>
      <div>Loading analytics data...</div>
    </div>

    <!-- Error State -->
    <div class="error-container" *ngIf="isAuthenticated() && error()">
      <div class="error-message">
        <h3>Error Loading Data</h3>
        <p>{{ error() }}</p>
        <button class="retry-button" (click)="refreshData()">Retry</button>
      </div>
    </div>

    <ng-container *ngIf="isAuthenticated() && !isLoading() && !error()">
      <!-- Budget Progress Section -->
      <div class="progress-section">
        <div class="savings-goal-progress">
          <app-progress-circle
            [percentage]="getSavingsGoalPercentage()"
            [label]="'Until Your Savings Goal'"
            [type]="'savings'"
            [showAmount]="true"
            [currentAmount]="savingsGoalProgress()?.totalCurrent"
            [totalAmount]="savingsGoalProgress()?.totalGoal"
            [currency]="'TND'">
          </app-progress-circle>
        </div>
        <div class="budget-spent-progress">
          <app-progress-circle
            [percentage]="getBudgetSpentPercentage()"
            [label]="'Of Budget Spent'"
            [type]="'budget'"
            [showAmount]="true"
            [currentAmount]="budgetSpentProgress()?.totalSpent"
            [totalAmount]="budgetSpentProgress()?.totalBudget"
            [currency]="'TND'">
          </app-progress-circle>
        </div>
      </div>

      <!-- Monthly Spending Section -->
      <div class="spending-section">
        <div class="spending-info">
          <div class="spending-amount">
            {{ getFormattedMonthlySpendingAmount() }}
            <span class="currency">TND</span>
          </div>
          <div class="spending-change">
            <span class="percentage" [ngClass]="getMonthlySpendingChange() > 0 ? 'increase' : 'decrease'">
              {{ getMonthlySpendingChange() }}%
            </span>
            <span class="compared-period">compared to last month</span>
          </div>
          <div class="spending-label">Average Spending Per Month</div>
        </div>
        <div class="trend-chart-container">
          <app-trend-chart [data]="getTrendData()"></app-trend-chart>
        </div>
      </div>

      <!-- Stats Grid -->
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-label">Monthly Percentage</div>
          <div class="stat-value">{{ getMonthlyPercentage() }}%</div>
          <div class="stat-change" [ngClass]="getMonthlySpendingChange() > 0 ? 'positive' : 'negative'">
            <span class="icon">{{ getMonthlySpendingChange() > 0 ? '↑' : '↓' }}</span>
            {{ getMonthlySpendingChange() }}% from last month
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-label">Top Category</div>
          <div class="stat-value">{{ getTopCategoryName() }}</div>
          <div class="stat-change" *ngIf="statsGrid()?.topCategory">
            {{ getFormattedTopCategoryAmount() }} TND ({{ getTopCategoryPercentage() }}%)
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-label">Top Product</div>
          <div class="stat-value" *ngIf="getProductExpenses().length > 0">{{ getProductExpenses()[0].name }}</div>
          <div class="stat-value" *ngIf="getProductExpenses().length === 0">No Data</div>
          <div class="stat-change" *ngIf="getProductExpenses().length > 0">
            {{ getFormattedTopProductAmount() }} TND ({{ getProductExpenses()[0].percentage }}%)
          </div>
        </div>
      </div>
      
      <!-- Expenses by Category -->
      <div class="category-expenses-card">
        <div class="card-header">
          <h3 class="card-title">Expenses By Category</h3>
        </div>
        <app-expense-list
          [expenses]="getCategoryExpenses()"
          [suffix]="'Per Category'">
        </app-expense-list>
      </div>

      <!-- Expenses by Product -->
      <div class="product-expenses-card">
        <div class="card-header">
          <h3 class="card-title">Expenses By Product</h3>
        </div>
        <app-expense-list
          [expenses]="getProductExpenses()"
          [suffix]="'Per Product'">
        </app-expense-list>
      </div>
    </ng-container>
  </div>
</div>