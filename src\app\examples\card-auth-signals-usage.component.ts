import { Component, inject, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthService } from '../core/auth/auth.service';
import { ExpenseTrackerService } from '../services/expense-tracker.service';
import { 
  LifetimeExpensesData, 
  CurrentMonthData, 
  ExpensesMonthData, 
  SavingPlanData 
} from '../interfaces/dashboard';

/**
 * Example component demonstrating how to use the new auth signals for the four card components
 * This shows how the card components integrate with authentication and provide user-specific data
 */
@Component({
  selector: 'app-card-auth-signals-usage',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="card-auth-example">
      <h2>Card Auth Signals Example</h2>
      
      <!-- Authentication Status -->
      <div class="auth-section">
        <h3>Authentication Status</h3>
        <div class="auth-info">
          <p><strong>Authenticated:</strong> {{ isAuthenticated() ? 'Yes' : 'No' }}</p>
          <p><strong>Current User:</strong> {{ currentUser()?.full_name || 'None' }}</p>
          <p><strong>Email:</strong> {{ currentUser()?.email || 'None' }}</p>
        </div>
      </div>

      <!-- Card Lifetime Expenses -->
      <div class="card-section" *ngIf="isAuthenticated()">
        <h3>Card Lifetime Expenses</h3>
        <div class="card-data" *ngIf="lifetimeExpenses(); else noLifetimeData">
          <p><strong>Total Amount:</strong> {{ lifetimeExpenses()!.totalAmount.toFixed(2) }} {{ lifetimeExpenses()!.currency }}</p>
          <p><strong>Transaction Count:</strong> {{ lifetimeExpenses()!.transactionCount }}</p>
          <p><strong>Account Created:</strong> {{ lifetimeExpenses()!.accountCreationDate | date:'medium' }}</p>
          <p><strong>First Transaction:</strong> {{ lifetimeExpenses()!.firstTransactionDate | date:'medium' }}</p>
        </div>
        <ng-template #noLifetimeData>
          <p>No lifetime expenses data available</p>
        </ng-template>
      </div>

      <!-- Card Current Month -->
      <div class="card-section" *ngIf="isAuthenticated()">
        <h3>Card Current Month</h3>
        <div class="card-data" *ngIf="currentMonth(); else noCurrentMonthData">
          <p><strong>Total Amount:</strong> {{ currentMonth()!.totalAmount.toFixed(2) }} {{ currentMonth()!.currency }}</p>
          <p><strong>Transaction Count:</strong> {{ currentMonth()!.transactionCount }}</p>
          <p><strong>Month/Year:</strong> {{ getMonthName(currentMonth()!.month) }} {{ currentMonth()!.year }}</p>
          <p><strong>Daily Breakdown:</strong> {{ currentMonth()!.dailyBreakdown.length }} days with data</p>
        </div>
        <ng-template #noCurrentMonthData>
          <p>No current month data available</p>
        </ng-template>
      </div>

      <!-- Card Expenses Month -->
      <div class="card-section" *ngIf="isAuthenticated()">
        <h3>Card Expenses Month</h3>
        <div class="card-data" *ngIf="expensesMonth(); else noExpensesMonthData">
          <p><strong>Total Amount:</strong> {{ expensesMonth()!.totalAmount.toFixed(2) }} {{ expensesMonth()!.currency }}</p>
          <p><strong>Average Monthly:</strong> {{ expensesMonth()!.averageMonthlyAmount.toFixed(2) }} {{ expensesMonth()!.currency }}</p>
          <p><strong>Months with Data:</strong> {{ expensesMonth()!.monthlyBreakdown.length }}</p>
          <div class="monthly-breakdown">
            <h4>Monthly Breakdown:</h4>
            <div class="month-item" *ngFor="let month of expensesMonth()!.monthlyBreakdown.slice(-6)">
              <span>{{ month.monthName }}: {{ month.amount.toFixed(2) }} {{ expensesMonth()!.currency }} ({{ month.transactionCount }} transactions)</span>
            </div>
          </div>
        </div>
        <ng-template #noExpensesMonthData>
          <p>No monthly expenses data available</p>
        </ng-template>
      </div>

      <!-- Card Saving Plan -->
      <div class="card-section" *ngIf="isAuthenticated()">
        <h3>Card Saving Plan</h3>
        <div class="card-data" *ngIf="savingPlan(); else noSavingPlanData">
          <p><strong>Total Target:</strong> {{ savingPlan()!.totalTarget.toFixed(2) }} {{ savingPlan()!.currency }}</p>
          <p><strong>Total Current:</strong> {{ savingPlan()!.totalCurrent.toFixed(2) }} {{ savingPlan()!.currency }}</p>
          <p><strong>Total Progress:</strong> {{ savingPlan()!.totalPercentage.toFixed(1) }}%</p>
          <div class="saving-plans">
            <h4>Saving Plans:</h4>
            <div class="plan-item" *ngFor="let plan of savingPlan()!.plans">
              <div class="plan-header">
                <span class="color-indicator" [style.background-color]="plan.color"></span>
                <span class="plan-name">{{ plan.name }}</span>
                <span class="plan-percentage">{{ plan.percentage.toFixed(1) }}%</span>
              </div>
              <div class="plan-details">
                <span>{{ plan.current.toFixed(2) }} / {{ plan.target.toFixed(2) }} {{ savingPlan()!.currency }}</span>
                <span *ngIf="plan.daysRemaining">({{ plan.daysRemaining }} days remaining)</span>
              </div>
            </div>
          </div>
        </div>
        <ng-template #noSavingPlanData>
          <p>No saving plan data available</p>
        </ng-template>
      </div>

      <!-- Summary -->
      <div class="summary-section" *ngIf="isAuthenticated()">
        <h3>Summary</h3>
        <div class="summary-data">
          <p><strong>Data Summary:</strong> {{ dataSummary() }}</p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .card-auth-example {
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }

    .auth-section, .card-section, .summary-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
      background: #f9f9f9;
    }

    .card-data {
      margin-top: 15px;
    }

    .monthly-breakdown, .saving-plans {
      margin-top: 15px;
    }

    .month-item, .plan-item {
      margin: 8px 0;
      padding: 8px;
      background: white;
      border-radius: 4px;
      border: 1px solid #eee;
    }

    .plan-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 5px;
    }

    .color-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }

    .plan-name {
      flex: 1;
      font-weight: 500;
    }

    .plan-percentage {
      font-weight: bold;
      color: #6B48FF;
    }

    .plan-details {
      font-size: 0.9em;
      color: #666;
    }

    h2, h3, h4 {
      color: #6B48FF;
    }

    p {
      margin: 8px 0;
    }
  `]
})
export class CardAuthSignalsUsageComponent {
  // Inject services
  private authService = inject(AuthService);
  private expenseTrackerService = inject(ExpenseTrackerService);

  // Signal-based reactive data
  isAuthenticated = computed(() => this.authService.isAuthenticated());
  currentUser = computed(() => this.authService.currentUser());

  // Mock implementations of the card auth signals for demonstration
  // In a real implementation, these would come from the dashboard component or a service
  lifetimeExpenses = computed((): LifetimeExpensesData | null => {
    const currentUser = this.currentUser();
    if (!currentUser?.email) return null;

    // Mock data for demonstration
    return {
      userId: currentUser.email,
      totalAmount: 2450.75,
      transactionCount: 45,
      accountCreationDate: new Date('2023-01-15'),
      firstTransactionDate: new Date('2023-01-20'),
      currency: 'TND'
    };
  });

  currentMonth = computed((): CurrentMonthData | null => {
    const currentUser = this.currentUser();
    if (!currentUser?.email) return null;

    const now = new Date();
    return {
      userId: currentUser.email,
      totalAmount: 345.50,
      transactionCount: 8,
      month: now.getMonth(),
      year: now.getFullYear(),
      currency: 'TND',
      dailyBreakdown: [
        { day: 1, amount: 45.20, transactionCount: 1 },
        { day: 5, amount: 120.30, transactionCount: 2 },
        { day: 12, amount: 180.00, transactionCount: 5 }
      ]
    };
  });

  expensesMonth = computed((): ExpensesMonthData | null => {
    const currentUser = this.currentUser();
    if (!currentUser?.email) return null;

    return {
      userId: currentUser.email,
      monthlyBreakdown: [
        { month: 0, year: 2024, amount: 450.20, transactionCount: 12, monthName: 'January 2024' },
        { month: 1, year: 2024, amount: 380.75, transactionCount: 9, monthName: 'February 2024' },
        { month: 2, year: 2024, amount: 520.30, transactionCount: 15, monthName: 'March 2024' }
      ],
      totalAmount: 1351.25,
      averageMonthlyAmount: 450.42,
      currency: 'TND'
    };
  });

  savingPlan = computed((): SavingPlanData | null => {
    const currentUser = this.currentUser();
    if (!currentUser?.email) return null;

    return {
      userId: currentUser.email,
      plans: [
        {
          id: 'food-savings',
          name: 'Food Savings',
          target: 200.00,
          current: 150.00,
          percentage: 75.0,
          color: '#A78BFA',
          category: 'food',
          daysRemaining: 45,
          isActive: true
        },
        {
          id: 'clothes-savings',
          name: 'Clothes Savings',
          target: 300.00,
          current: 120.00,
          percentage: 40.0,
          color: '#F9A8D4',
          category: 'clothes',
          daysRemaining: 120,
          isActive: true
        }
      ],
      totalTarget: 500.00,
      totalCurrent: 270.00,
      totalPercentage: 54.0,
      currency: 'TND'
    };
  });

  // Helper computed signal for summary
  dataSummary = computed(() => {
    const lifetime = this.lifetimeExpenses();
    const currentMonth = this.currentMonth();
    const savingPlan = this.savingPlan();
    
    if (!lifetime || !currentMonth || !savingPlan) {
      return 'No data available - please authenticate';
    }
    
    return `Lifetime: ${lifetime.totalAmount.toFixed(2)} TND (${lifetime.transactionCount} transactions), ` +
           `This month: ${currentMonth.totalAmount.toFixed(2)} TND (${currentMonth.transactionCount} transactions), ` +
           `Savings progress: ${savingPlan.totalPercentage.toFixed(1)}%`;
  });

  // Helper method for month names
  getMonthName(monthIndex: number): string {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[monthIndex] || 'Unknown';
  }
}
