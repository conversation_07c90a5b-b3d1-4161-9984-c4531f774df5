import { Component, OnInit, OnDestroy, inject, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { SavingPlan } from '../models/financial-savings.model';
import { FinancialSavingsService } from '../services/financial-savings.service';
import { ThemeService } from '../services/theme.service';
import { AuthWarningComponent } from '../shared/auth-warning/auth-warning.component';
import { AuthService } from '../core/auth/auth.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-financial-savings',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TopNavbarComponent,
    SidebarComponent,
    AuthWarningComponent
  ],
  templateUrl: './financial-savings.component.html',
  styleUrls: ['./financial-savings.component.scss']
})
export class FinancialSavingsComponent implements OnInit, OnDestroy {
  // Inject services
  private savingsService = inject(FinancialSavingsService);
  private router = inject(Router);
  private themeService = inject(ThemeService);
  private authService = inject(AuthService);

  // Auth signals
  isAuthenticated = computed(() => this.authService.isAuthenticated());
  currentUser = computed(() => this.authService.currentUser());

  savingPlans: SavingPlan[] = [];
  isDarkMode: boolean = false;
  private destroy$ = new Subject<void>();

  ngOnInit(): void {
    // Subscribe to theme changes
    this.themeService.isDarkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isDark => {
        this.isDarkMode = isDark;
      });

    // Get saving plans
    this.savingsService.getSavingPlans().subscribe(plans => {
      this.savingPlans = plans;
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  editSavingPlan(id: string): void {
    this.router.navigate(['/edit-financial-savings', id]);
  }

  createNewSavingPlan(): void {
    this.router.navigate(['/edit-financial-savings']);
  }

  // Format date to display in a readable format
  formatDate(date: Date): string {
    if (!date) return '';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  // Get duration conversion display for saving plan item
  getDurationConversion(plan: SavingPlan): string {
    if (!plan.durationValue || plan.durationValue <= 0 || !plan.durationType) {
      return '';
    }

    if (plan.durationType === 'weeks' && plan.durationValue > 4) {
      const months = Math.round((plan.durationValue / 4) * 10) / 10; // 4 weeks per month
      return `(${months} Months)`;
    }

    if (plan.durationType === 'months' && plan.durationValue > 12) {
      const years = Math.round((plan.durationValue / 12) * 10) / 10; // 12 months per year
      return `(${years} Years)`;
    }

    return '';
  }
}
