import { CanActivateFn, Router } from '@angular/router';
import { inject } from '@angular/core';
import { AuthService } from './auth.service';

export const roleGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  const currentUser = authService.currentUser();

  // If user is not authenticated, allow navigation (other guards will handle auth)
  if (!currentUser) {
    return true;
  }

  const userRole = currentUser.role;
  const currentPath = state.url;

  // If shopper tries to access seller routes, redirect to shopper dashboard
  if (userRole === 'shopper' && (currentPath.startsWith('/seller') || currentPath.startsWith('/dashboard') || currentPath.startsWith('/analytics'))) {
    router.navigate(['/shopper-dashboard']);
    return false;
  }

  // If seller tries to access shopper routes, redirect to seller dashboard
  if (userRole === 'seller' && currentPath.startsWith('/shopper')) {
    router.navigate(['/seller/dashboard']);
    return false;
  }

  return true;
};
