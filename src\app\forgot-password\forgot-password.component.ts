import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-forgot-password',
  standalone: true,
  imports: [FormsModule, CommonModule, RouterModule],
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.css']
})
export class ForgotPasswordComponent {
  model = {
    email: ''
  };
  errorMessage: string | null = null;

  constructor(private router: Router) {}

  onSubmit() {
    if (this.model.email) {
      console.log('Password reset link sent to:', this.model.email);
      this.errorMessage = null;
      // You can add actual email sending logic here
      alert('Password reset link has been sent to your email!');
    } else {
      this.errorMessage = 'Please enter a valid email address.';
    }
  }

  goBack() {
    // Navigate back to login page instead of home
    this.router.navigate(['/login']);
  }
}