import { TestBed } from '@angular/core/testing';
import { signal } from '@angular/core';
import { CardAuthDataService } from './card-auth-data.service';
import { AuthService } from '../auth/auth.service';
import { ExpenseTrackerService } from '../../services/expense-tracker.service';
import { FinancialSavingsDataService } from '../financial-savings/financial-savings-data.service';

describe('CardAuthDataService', () => {
  let service: CardAuthDataService;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockExpenseTrackerService: jasmine.SpyObj<ExpenseTrackerService>;
  let mockFinancialSavingsDataService: jasmine.SpyObj<FinancialSavingsDataService>;

  const mockUser = {
    email: '<EMAIL>',
    full_name: 'Test User',
    avatar: 'assets/images/default-avatar.svg',
    is_initialized: true
  };

  const mockTransactions = [
    {
      date: '2024-01-15',
      amount: '150.50 TND',
      ticketNumber: 'T001',
      productName: 'Test Product 1'
    },
    {
      date: '2024-02-10',
      amount: '200.75 TND',
      ticketNumber: 'T002',
      productName: 'Test Product 2'
    },
    {
      date: '2024-03-05',
      amount: '99.25 TND',
      ticketNumber: 'T003',
      productName: 'Test Product 3'
    }
  ];

  const mockCategoryExpenses = [
    { category: 'food', amount: 300, color: '#A78BFA' },
    { category: 'clothes', amount: 200, color: '#F9A8D4' },
    { category: 'leisure', amount: 150, color: '#60A5FA' }
  ];

  const mockSavingPlans = [
    {
      id: '1',
      name: 'Vacation Fund',
      objective: 2000,
      current: 500,
      period: '6 months' as any,
      frequency: 'Monthly' as any,
      startDate: new Date('2024-01-01'),
      lastUpdate: new Date(),
      userId: '<EMAIL>',
      percentComplete: 25
    },
    {
      id: '2',
      name: 'Emergency Fund',
      objective: 5000,
      current: 1500,
      period: '1 year' as any,
      frequency: 'Monthly' as any,
      startDate: new Date('2024-01-01'),
      lastUpdate: new Date(),
      userId: '<EMAIL>',
      percentComplete: 30
    }
  ];

  beforeEach(() => {
    const authServiceSpy = jasmine.createSpyObj('AuthService', [], {
      currentUser: jasmine.createSpy('currentUser').and.returnValue(mockUser),
      isAuthenticated: jasmine.createSpy('isAuthenticated').and.returnValue(true)
    });
    const expenseTrackerServiceSpy = jasmine.createSpyObj('ExpenseTrackerService', ['getCurrentExpenses']);
    const financialSavingsDataServiceSpy = jasmine.createSpyObj('FinancialSavingsDataService', [], {
      plans: jasmine.createSpy('plans').and.returnValue(mockSavingPlans)
    });

    TestBed.configureTestingModule({
      providers: [
        CardAuthDataService,
        { provide: AuthService, useValue: authServiceSpy },
        { provide: ExpenseTrackerService, useValue: expenseTrackerServiceSpy },
        { provide: FinancialSavingsDataService, useValue: financialSavingsDataServiceSpy }
      ]
    });

    service = TestBed.inject(CardAuthDataService);
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    mockExpenseTrackerService = TestBed.inject(ExpenseTrackerService) as jasmine.SpyObj<ExpenseTrackerService>;
    mockFinancialSavingsDataService = TestBed.inject(FinancialSavingsDataService) as jasmine.SpyObj<FinancialSavingsDataService>;

    // Setup localStorage mock
    spyOn(localStorage, 'getItem').and.returnValue(JSON.stringify(mockTransactions));
    spyOn(localStorage, 'setItem');

    // Setup service mocks
    mockExpenseTrackerService.getCurrentExpenses.and.returnValue(mockCategoryExpenses);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should calculate lifetime expenses correctly', () => {
    // Trigger the effect by setting up the user
    service['_currentUserEmail'].set(mockUser.email);
    service['_allTransactions'].set(mockTransactions);

    const lifetimeExpenses = service.cardLifetimeExpenses();

    expect(lifetimeExpenses).toBeTruthy();
    expect(lifetimeExpenses!.userId).toBe(mockUser.email);
    expect(lifetimeExpenses!.totalAmount).toBe(450.5); // 150.5 + 200.75 + 99.25
    expect(lifetimeExpenses!.transactionCount).toBe(3);
    expect(lifetimeExpenses!.currency).toBe('TND');
  });

  it('should calculate current month expenses correctly', () => {
    const currentDate = new Date();
    const currentMonthTransactions = mockTransactions.filter(t => {
      const transactionDate = new Date(t.date);
      return transactionDate.getMonth() === currentDate.getMonth() &&
             transactionDate.getFullYear() === currentDate.getFullYear();
    });

    service['_currentUserEmail'].set(mockUser.email);
    service['_allTransactions'].set(mockTransactions);

    const currentMonth = service.cardCurrentMonth();

    expect(currentMonth).toBeTruthy();
    expect(currentMonth!.userId).toBe(mockUser.email);
    expect(currentMonth!.month).toBe(currentDate.getMonth());
    expect(currentMonth!.year).toBe(currentDate.getFullYear());
    expect(currentMonth!.transactionCount).toBe(currentMonthTransactions.length);
  });

  it('should calculate monthly expenses breakdown correctly', () => {
    service['_currentUserEmail'].set(mockUser.email);
    service['_allTransactions'].set(mockTransactions);

    const expensesMonth = service.cardExpensesMonth();

    expect(expensesMonth).toBeTruthy();
    expect(expensesMonth!.userId).toBe(mockUser.email);
    expect(expensesMonth!.monthlyBreakdown.length).toBeGreaterThan(0);
    expect(expensesMonth!.totalAmount).toBe(450.5);
    expect(expensesMonth!.currency).toBe('TND');
  });

  it('should calculate saving plan data correctly from Financial Savings', () => {
    service['_currentUserEmail'].set(mockUser.email);

    const savingPlan = service.cardSavingPlan();

    expect(savingPlan).toBeTruthy();
    expect(savingPlan!.userId).toBe(mockUser.email);
    expect(savingPlan!.plans.length).toBe(mockSavingPlans.length);
    expect(savingPlan!.currency).toBe('TND');

    // Check that each plan has the expected structure from Financial Savings
    savingPlan!.plans.forEach((plan, index) => {
      expect(plan.id).toBe(mockSavingPlans[index].id);
      expect(plan.name).toBe(mockSavingPlans[index].name);
      expect(plan.target).toBe(mockSavingPlans[index].objective);
      expect(plan.current).toBe(mockSavingPlans[index].current);
      expect(plan.category).toBe('savings');
      expect(plan.isActive).toBe(true);
      expect(plan.color).toBeDefined(); // Color should be assigned from palette
    });

    // Check totals
    expect(savingPlan!.totalTarget).toBe(7000); // 2000 + 5000
    expect(savingPlan!.totalCurrent).toBe(2000); // 500 + 1500
  });

  it('should return null for all signals when user is not authenticated', () => {
    mockAuthService.currentUser.and.returnValue(null);
    mockAuthService.isAuthenticated.and.returnValue(false);

    expect(service.cardLifetimeExpenses()).toBeNull();
    expect(service.cardCurrentMonth()).toBeNull();
    expect(service.cardExpensesMonth()).toBeNull();
    expect(service.cardSavingPlan()).toBeNull();
  });

  it('should provide helper methods with correct values', () => {
    service['_currentUserEmail'].set(mockUser.email);
    service['_allTransactions'].set(mockTransactions);

    expect(service.getLifetimeExpensesTotal()).toBe(450.5);
    expect(service.getCurrentMonthTotal()).toBeGreaterThanOrEqual(0);
    expect(service.getMonthlyExpensesData()).toEqual(jasmine.any(Array));
    expect(service.getSavingPlanItems()).toEqual(jasmine.any(Array));
    expect(service.getTotalSavingsPercentage()).toBeGreaterThanOrEqual(0);
  });

  it('should refresh transactions when refreshTransactions is called', () => {
    service['_currentUserEmail'].set(mockUser.email);
    
    service.refreshTransactions();

    expect(localStorage.getItem).toHaveBeenCalledWith(`receeto-transactions_${mockUser.email}`);
  });
});
