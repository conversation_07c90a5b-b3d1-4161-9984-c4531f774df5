.sidebar {
    width: 250px;
  background: #5338c3;
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1000;
  transition: transform 0.3s ease;
}

.sidebar-top {
  flex-grow: 1;
}

.sidebar-bottom {
  margin-top: auto;
  padding-top: 20px;
}

.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.sidebar-overlay.open {
  opacity: 1;
  visibility: visible;
}

.sidebar.open {
  transform: translateX(0);
}

.logo {
  display: flex;
  align-items: center;
  padding: 10px 0;
  margin-bottom: 30px;
  cursor: pointer;
}

.logo-image {
  width: 120px;
  height: 40px;
  background-image: url('../../assets/images/Recetto.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.arrow-icon {
  margin-left: auto;
  font-size: 16px;
  cursor: pointer;
}

.dropdown-window {
  position: absolute;
  top: 110px; /* Adjusted for larger logo container */
  left: 15px;
  right: 15px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  z-index: 1000;
  padding: 20px;
  animation: slideDown 0.3s ease-out;
  border: 2px solid #000000;
}

.plan-comparison {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.plan-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  position: relative;
  transition: transform 0.2s ease;
}

.plan-card:hover {
  transform: translateY(-2px);
}

.plan-header {
  text-align: center;
  margin-bottom: 15px;
}

.plan-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.plan-header h3 {
  color: #2d3436;
  font-size: 18px;
  margin: 8px 0;
}

.plan-price {
  display: block;
  color: #6c757d;
  font-size: 14px;
}

.plan-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.plan-features li {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  color: #4a4a4a;
  font-size: 14px;
}

.check {
  color: #00b894;
  font-weight: bold;
}

.disabled {
  color: #cbd5e0;
}

.premium {
  background: linear-gradient(145deg, #6b48ff0f, #a855f70f);
  border: 2px solid #6b48ff;
}

.premium-badge {
  position: absolute;
  top: -12px;
  right: 10px;
  background: #6b48ff;
  color: white;
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: bold;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.normal-user {
  background: #FFFFFF;
  border: 2px solid #34C759;
  border-radius: 8px;
  padding: 15px;
  margin: 10px;
  color: #333;
}

.premium-user {
  background: #FFFFFF;
  border: 2px solid #FFD700;
  border-radius: 8px;
  padding: 15px;
  margin: 10px;
  color: #333;
}

.normal-user h3,
.premium-user h3 {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.normal-user p,
.premium-user p {
  font-size: 14px;
  margin: 5px 0;
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 30px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  color: white;
  text-decoration: none;
  font-size: 16px;
  border-radius: 8px;
  transition: background 0.2s ease;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
  background: rgba(255, 255, 255, 0.2);
}

.nav-item .icon {
  margin-right: 12px;
  font-size: 18px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Specific icon styles */
.nav-item .fa-th-large {
  color: #ffffff;
}

.nav-item .fa-chart-line {
  color: #ffffff;
}

.nav-item .fa-table {
  color: #ffffff;
}

.nav-item .fa-plus-circle {
  color: #ffffff;
}

.nav-item .fa-bell {
  color: #ffffff;
}

.nav-item .fa-receipt {
  color: #ffffff;
}

/* Hover state for icons */
.nav-item:hover .icon {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

/* Active state for icons */
.nav-item.active .icon {
  color: #ffffff;
  opacity: 1;
}

.upgrade-box {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 20px;
}

.upgrade-box p {
  margin: 0 0 10px;
  font-size: 14px;
}

.upgrade-btn {
  background: #ffffff;
  color: #6b48ff;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.2s ease;
}

.upgrade-btn:hover {
  background: #e6e6e6;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.2);
}

.avatar-placeholder {
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-icon {
  width: 24px;
  height: 24px;
  color: white;
  flex-shrink: 0;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-name {
  font-size: 1rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

.user-role {
  font-size: 0.85rem;
  color: #f1e100;
  margin: 0;
}

.user-info-dropdown {
  position: absolute;
  bottom: 80px;
  left: 15px;
  right: 15px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  z-index: 1000;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-info-menu {
  display: flex;
  flex-direction: column;
}

.user-menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  color: #333;
  text-decoration: none;
  transition: background-color 0.2s ease;
}

.user-menu-item:hover {
  background-color: #f8f9fa;
}

.user-menu-item i {
  font-size: 18px;
  color: #6b48ff;
  width: 20px;
  text-align: center;
}

.logout-btn {
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
}

.logout-btn:hover {
  background-color: #f8f9fa;
}

.logo-link {
  text-decoration: none;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.logo-link:hover {
  opacity: 0.8;
}

@media (min-width: 769px) {
  .sidebar {
    transform: translateX(0); /* Always visible on PC */
  }

  .sidebar-overlay {
    display: none; /* No overlay on PC */
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    width: 250px;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .logo {
    min-height: 80px; /* Adjusted for smaller screens */
    padding: 8px 0;
  }

  .logo img {
    width: 60px; /* Reduced for smaller screens */
    height: 60px;
    object-fit: contain;
    margin-right: 8px;
    border-radius: 6px;
  }

  .arrow-icon {
    font-size: 14px;
  }

  .dropdown-window {
    top: 90px; /* Adjusted for smaller logo */
    left: 10px;
    right: 10px;
    padding: 15px;
  }

  .normal-user,
  .premium-user {
    margin: 8px;
    padding: 12px;
  }

  .normal-user h3,
  .premium-user h3 {
    font-size: 14px;
  }

  .normal-user p,
  .premium-user p {
    font-size: 12px;
  }

  .nav-item {
    font-size: 14px;
    padding: 10px 12px;
  }

  .icon {
    font-size: 18px;
  }

  .upgrade-box {
    padding: 12px;
  }

  .upgrade-btn {
    padding: 9px 15px;
    font-size: 13px;
  }

  .avatar-placeholder {
    width: 36px;
    height: 36px;
    min-width: 36px;
    min-height: 36px;
  }

  .avatar-icon {
    width: 20px;
    height: 20px;
  }

  .user-name {
    font-size: 14px;
  }

  .user-role {
    font-size: 12px;
  }

  .plan-card {
    padding: 15px;
  }

  .plan-features li {
    font-size: 13px;
  }
}

/* Update user avatar visibility for mobile */
@media (max-width: 768px) {
  /* ...existing code... */

  .header-avatar img {
    display: none; /* Hide the duplicate avatar */
  }

  .avatar-placeholder {
    width: 36px;
    height: 36px;
    min-width: 36px;
    min-height: 36px;
    display: flex; /* Keep the sidebar avatar visible */
  }

  .avatar-icon {
    width: 20px;
    height: 20px;
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 220px;
  }

  .logo {
    min-height: 70px; /* Adjusted for very small screens */
    padding: 6px 0;
  }

  .logo img {
    width: 50px; /* Further reduced for very small screens */
    height: 50px;
    object-fit: contain;
    margin-right: 6px;
    border-radius: 6px;
  }

  .arrow-icon {
    font-size: 12px;
  }

  .dropdown-window {
    top: 80px; /* Adjusted for smaller logo */
    left: 10px;
    right: 10px;
  }

  .normal-user,
  .premium-user {
    margin: 6px;
    padding: 10px;
  }

  .normal-user h3,
  .premium-user h3 {
    font-size: 12px;
  }

  .normal-user p,
  .premium-user p {
    font-size: 10px;
  }

  .nav-item {
    font-size: 13px;
    padding: 8px 10px;
  }

  .icon {
    font-size: 16px;
  }

  .upgrade-box {
    padding: 10px;
  }

  .upgrade-btn {
    padding: 8px 14px;
    font-size: 12px;
  }

  .avatar-placeholder {
    width: 32px;
    height: 32px;
    min-width: 32px;
    min-height: 32px;
  }

  .avatar-icon {
    width: 18px;
    height: 18px;
  }

  .user-name {
    font-size: 13px;
  }

  .user-role {
    font-size: 11px;
  }
}