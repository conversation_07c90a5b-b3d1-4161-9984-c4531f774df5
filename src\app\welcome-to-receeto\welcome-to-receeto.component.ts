import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../core/auth/auth.service';
import { UserDataService } from '../core/user-data/user-data.service';
import { AppStorageService } from '../services/app-storage.service';
import { NotificationService } from '../notification-system/notification.service';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-welcome-to-receeto',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './welcome-to-receeto.component.html',
  styleUrls: ['./welcome-to-receeto.component.css']
})
export class WelcomeToReceetoComponent {
  private authService = inject(AuthService);
  private userDataService = inject(UserDataService);
  private appStorageService = inject(AppStorageService);
  private notificationService = inject(NotificationService);

  // Development mode flag
  isDevelopmentMode = true;
  showResetConfirmation = false;

  constructor(private router: Router) {}

  selectRole(role: 'shopper' | 'seller') {
    this.router.navigate(['/login'], { queryParams: { role: role } });
  }

  /**
   * Reset all application data for development purposes
   */
  resetAllData(): void {
    this.showResetConfirmation = true;
  }

  /**
   * Confirm and execute the data reset
   */
  confirmResetData(): void {
    console.log('=== DEVELOPMENT MODE: RESETTING ALL DATA ===');

    try {
      // 1. Clear all localStorage data
      localStorage.clear();
      console.log('✅ localStorage cleared');

      // 2. Reset AuthService
      this.authService.logout();
      console.log('✅ AuthService reset');

      // 3. Reset UserDataService
      this.userDataService.clearUserData();
      console.log('✅ UserDataService reset');

      // 4. Reset AppStorageService (budgets, loyalty cards, etc.)
      this.appStorageService.clearAllData();
      console.log('✅ AppStorageService reset');

      // 5. Clear notifications
      this.notificationService.clearAllNotifications();
      this.notificationService.clearCurrentUser();
      console.log('✅ NotificationService reset');

      console.log('=== ALL APPLICATION DATA RESET COMPLETE ===');

      // Show success message
      alert('✅ All data has been reset successfully!\n\nAll user accounts, profiles, budgets, loyalty cards, and notifications have been cleared.');

      // Hide confirmation dialog
      this.showResetConfirmation = false;

      // Reload the page to ensure clean state
      setTimeout(() => {
        window.location.reload();
      }, 1000);

    } catch (error) {
      console.error('Error during data reset:', error);
      alert('❌ Error occurred during data reset. Check console for details.');
      this.showResetConfirmation = false;
    }
  }

  /**
   * Cancel the reset operation
   */
  cancelResetData(): void {
    this.showResetConfirmation = false;
  }
}