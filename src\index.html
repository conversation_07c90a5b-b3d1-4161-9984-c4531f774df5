<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Recetto - Smart Shopping & Analytics</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
  <meta name="description" content="Recetto - Your smart shopping companion with analytics, budget tracking, and loyalty card management">
  <meta name="theme-color" content="#6B48FF">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="Recetto">

  <!-- Preconnect to external domains for faster loading -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="preconnect" href="https://cdnjs.cloudflare.com">
  <link rel="preconnect" href="https://cdn.jsdelivr.net">

  <!-- Favicon and app icons -->
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <link rel="apple-touch-icon" href="assets/images/Recetto-Login_Register-Logo.png">

  <!-- Preload critical resources -->
  <link rel="preload" href="assets/images/Recetto-Login_Register-Logo.png" as="image">
  <link rel="preload" href="assets/images/default-avatar.svg" as="image">

  <!-- Optimized external stylesheets with preload -->
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&family=Inter:wght@400;500&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <link rel="preload" href="https://fonts.googleapis.com/icon?family=Material+Icons" as="style" onload="this.onload=null;this.rel='stylesheet'">
  <link rel="preload" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">

  <!-- Fallback for browsers that don't support preload -->
  <noscript>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&family=Inter:wght@400;500&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
  </noscript>

  <!-- Critical CSS for loading state -->
  <style>
    /* Critical CSS for above-the-fold content */
    body {
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: #f8fafc;
      color: #1a202c;
      overflow-x: hidden;
    }

    app-root {
      display: block;
      min-height: 100vh;
    }

    /* Loading spinner */
    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f8fafc;
      z-index: 9999;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #e2e8f0;
      border-top: 4px solid #6B48FF;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-text {
      margin-top: 16px;
      color: #64748b;
      font-size: 14px;
      text-align: center;
    }
  </style>
</head>
<body>
  <!-- Loading screen -->
  <div class="loading-container" id="initial-loader">
    <div>
      <div class="loading-spinner"></div>
      <div class="loading-text">Loading Recetto...</div>
    </div>
  </div>

  <app-root></app-root>

  <!-- Performance and loading optimization scripts -->
  <script>
    // Remove loading screen when Angular app is ready
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(function() {
        const loader = document.getElementById('initial-loader');
        if (loader) {
          loader.style.opacity = '0';
          loader.style.transition = 'opacity 0.3s ease';
          setTimeout(() => loader.remove(), 300);
        }
      }, 1500);
    });

    // Performance monitoring
    if ('performance' in window) {
      window.addEventListener('load', function() {
        setTimeout(function() {
          const perfData = performance.getEntriesByType('navigation')[0];
          if (perfData) {
            console.log('Page Load Performance:', {
              'DOM Content Loaded': Math.round(perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart),
              'Load Complete': Math.round(perfData.loadEventEnd - perfData.loadEventStart),
              'First Byte': Math.round(perfData.responseStart - perfData.requestStart)
            });
          }
        }, 0);
      });
    }

    // Preload critical resources
    function preloadCriticalResources() {
      const criticalImages = [
        'assets/images/Recetto-Login_Register-Logo.png',
        'assets/images/default-avatar.svg',
        'assets/images/default-brand-logo.png'
      ];

      criticalImages.forEach(function(src) {
        const img = new Image();
        img.src = src;
      });
    }

    // Start preloading immediately
    preloadCriticalResources();
  </script>
</body>
</html>