import { Injectable, signal, computed, effect, inject } from '@angular/core';
import { Observable, of } from 'rxjs';
import { AuthService } from '../auth/auth.service';
import { BudgetDataService } from '../budget/budget-data.service';
import { FinancialSavingsDataService } from '../financial-savings/financial-savings-data.service';
import { CardAuthDataService } from '../dashboard/card-auth-data.service';
import {
  ShopperAnalyticsData,
  ShopperAnalyticsState,
  SavingsGoalProgressData,
  BudgetSpentProgressData,
  SpendingSectionData,
  StatsGridData,
  CategoryExpensesData,
  ProductExpensesData,
  SavingsGoalItem,
  BudgetSpentItem,
  MonthlyBreakdownItem,
  CategoryStatsItem,
  CategoryExpenseItem,
  ProductExpenseItem,
  DEFAULT_SHOPPER_ANALYTICS_CONFIG
} from '../../interfaces/shopper-analytics';

@Injectable({
  providedIn: 'root'
})
export class ShopperAnalyticsDataService {
  private authService = inject(AuthService);
  private budgetDataService = inject(BudgetDataService);
  private financialSavingsDataService = inject(FinancialSavingsDataService);
  private cardAuthDataService = inject(CardAuthDataService);

  // Private signals for shopper analytics data
  private _analyticsState = signal<ShopperAnalyticsState>({
    analytics: null,
    savingsGoalProgress: null,
    budgetSpentProgress: null,
    spendingSection: null,
    statsGrid: null,
    categoryExpenses: null,
    productExpenses: null,
    isLoading: false,
    lastSync: null,
    error: null
  });

  // Public computed signals for reactive access
  public readonly analyticsState = computed(() => this._analyticsState());
  public readonly analytics = computed(() => this._analyticsState().analytics);
  public readonly savingsGoalProgress = computed(() => this._analyticsState().savingsGoalProgress);
  public readonly budgetSpentProgress = computed(() => this._analyticsState().budgetSpentProgress);
  public readonly spendingSection = computed(() => this._analyticsState().spendingSection);
  public readonly statsGrid = computed(() => this._analyticsState().statsGrid);
  public readonly categoryExpenses = computed(() => this._analyticsState().categoryExpenses);
  public readonly productExpenses = computed(() => this._analyticsState().productExpenses);
  public readonly isLoading = computed(() => this._analyticsState().isLoading);
  public readonly error = computed(() => this._analyticsState().error);

  constructor() {
    // Effect to load analytics data when user changes
    effect(() => {
      const currentUser = this.authService.currentUser();
      if (currentUser) {
        this.loadAnalyticsData(currentUser.email);
      } else {
        this.clearAnalyticsData();
      }
    });

    // Effect to reactively update analytics when underlying data changes
    effect(() => {
      const currentUser = this.authService.currentUser();
      if (!currentUser) return;

      // Watch for changes in budget data
      const budgets = this.budgetDataService.budgets();
      const totalBudget = this.budgetDataService.totalBudget();
      const totalSpent = this.budgetDataService.totalSpent();

      // Watch for changes in financial savings data
      const savingsPlans = this.financialSavingsDataService.plans();
      const totalObjective = this.financialSavingsDataService.totalObjective();
      const totalCurrent = this.financialSavingsDataService.totalCurrent();

      // Watch for changes in card auth data
      const lifetimeExpenses = this.cardAuthDataService.cardLifetimeExpenses();
      const currentMonth = this.cardAuthDataService.cardCurrentMonth();
      const expensesMonth = this.cardAuthDataService.cardExpensesMonth();
      const savingPlan = this.cardAuthDataService.cardSavingPlan();

      // Only reload if we have a user and the current analytics is null or outdated
      const currentAnalytics = this._analyticsState().analytics;
      if (currentUser && !currentAnalytics) {
        console.log('Analytics data dependencies changed, loading analytics for first time...');
        this.loadAnalyticsData(currentUser.email);
      } else if (currentUser && currentAnalytics &&
                 (budgets.length > 0 || savingsPlans.length > 0 || lifetimeExpenses || currentMonth || expensesMonth || savingPlan)) {
        // Only update if data has actually changed (avoid infinite loops)
        const lastUpdate = currentAnalytics.lastUpdated;
        const now = new Date();
        const timeDiff = now.getTime() - lastUpdate.getTime();

        // Only update if it's been more than 1 second since last update (debounce)
        if (timeDiff > 1000) {
          console.log('Analytics data dependencies changed, updating analytics...');
          this.loadAnalyticsData(currentUser.email);
        }
      }
    }, { allowSignalWrites: true });
  }

  /**
   * Load analytics data for the current user
   */
  private loadAnalyticsData(userEmail: string): void {
    console.log(`Loading analytics data for user: ${userEmail}`);
    this._analyticsState.update(state => ({ ...state, isLoading: true, error: null }));

    try {
      // Load data from various sources
      console.log('Building analytics components...');
      const savingsGoalProgress = this.buildSavingsGoalProgress(userEmail);
      const budgetSpentProgress = this.buildBudgetSpentProgress(userEmail);
      const spendingSection = this.buildSpendingSection(userEmail);
      const statsGrid = this.buildStatsGrid(userEmail);
      const categoryExpenses = this.buildCategoryExpenses(userEmail);
      const productExpenses = this.buildProductExpenses(userEmail);

      const analyticsData: ShopperAnalyticsData = {
        id: `analytics_${userEmail}_${Date.now()}`,
        userId: userEmail,
        lastUpdated: new Date(),
        savingsGoalProgress,
        budgetSpentProgress,
        spendingSection,
        statsGrid,
        categoryExpenses,
        productExpenses
      };

      this._analyticsState.set({
        analytics: analyticsData,
        savingsGoalProgress,
        budgetSpentProgress,
        spendingSection,
        statsGrid,
        categoryExpenses,
        productExpenses,
        isLoading: false,
        lastSync: new Date(),
        error: null
      });

      console.log(`Successfully loaded shopper analytics data for user: ${userEmail}`, {
        savingsGoalProgress,
        budgetSpentProgress,
        spendingSection
      });
    } catch (error) {
      console.error('Error loading shopper analytics data:', error);
      this._analyticsState.update(state => ({
        ...state,
        isLoading: false,
        error: 'Failed to load analytics data'
      }));
    }
  }

  /**
   * Build savings goal progress data from financial savings service
   */
  private buildSavingsGoalProgress(userEmail: string): SavingsGoalProgressData {
    const savingsPlans = this.financialSavingsDataService.plans();
    const totalObjective = this.financialSavingsDataService.totalObjective();
    const totalCurrent = this.financialSavingsDataService.totalCurrent();

    console.log('Building savings goal progress:', {
      plansCount: savingsPlans.length,
      totalObjective,
      totalCurrent
    });

    const plans: SavingsGoalItem[] = savingsPlans.map((plan, index) => ({
      id: plan.id,
      name: plan.name,
      goal: plan.objective,
      current: plan.current,
      percentage: plan.objective > 0 ? Math.round((plan.current / plan.objective) * 100) : 0,
      color: this.getColorForIndex(index),
      isActive: plan.current < plan.objective
    }));

    const result = {
      userId: userEmail,
      totalGoal: totalObjective,
      totalCurrent: totalCurrent,
      percentage: totalObjective > 0 ? Math.round((totalCurrent / totalObjective) * 100) : 0,
      currency: 'TND',
      plans
    };

    console.log('Savings goal progress result:', result);
    return result;
  }

  /**
   * Build budget spent progress data from budget service
   */
  private buildBudgetSpentProgress(userEmail: string): BudgetSpentProgressData {
    const budgets = this.budgetDataService.budgets();
    const totalBudget = this.budgetDataService.totalBudget();
    const totalSpent = this.budgetDataService.totalSpent();

    console.log('Building budget spent progress:', {
      budgetsCount: budgets.length,
      totalBudget,
      totalSpent
    });

    const categories: BudgetSpentItem[] = budgets.map((budget, index) => ({
      id: budget.id,
      name: budget.name,
      budget: budget.amount,
      spent: budget.spent || 0,
      percentage: budget.amount > 0 ? Math.round(((budget.spent || 0) / budget.amount) * 100) : 0,
      color: this.getColorForIndex(index),
      isOverBudget: (budget.spent || 0) > budget.amount
    }));

    const result = {
      userId: userEmail,
      totalBudget,
      totalSpent,
      percentage: totalBudget > 0 ? Math.round((totalSpent / totalBudget) * 100) : 0,
      currency: 'TND',
      categories
    };

    console.log('Budget spent progress result:', result);
    return result;
  }

  /**
   * Build spending section data from card expenses month
   */
  private buildSpendingSection(userEmail: string): SpendingSectionData {
    const expensesMonthData = this.cardAuthDataService.cardExpensesMonth();

    console.log('Building spending section:', { expensesMonthData });

    if (!expensesMonthData || !expensesMonthData.monthlyBreakdown.length) {
      console.log('No expenses month data found, returning default');
      return {
        userId: userEmail,
        monthlySpending: {
          amount: 0,
          percentage: 0,
          percentageChange: 0,
          comparedPeriod: 'last month',
          monthlyBreakdown: []
        },
        trendData: [0, 0, 0, 0, 0, 0, 0],
        currency: 'TND'
      };
    }

    const monthlyBreakdown: MonthlyBreakdownItem[] = expensesMonthData.monthlyBreakdown.map(month => ({
      month: month.monthName,
      year: month.year,
      amount: month.amount,
      transactionCount: month.transactionCount
    }));

    // Calculate trend data from last 7 months
    const trendData = monthlyBreakdown.slice(-7).map(month => month.amount);

    // Calculate percentage change from previous month
    const currentMonth = monthlyBreakdown[monthlyBreakdown.length - 1];
    const previousMonth = monthlyBreakdown[monthlyBreakdown.length - 2];
    const percentageChange = previousMonth && previousMonth.amount > 0
      ? Math.round(((currentMonth.amount - previousMonth.amount) / previousMonth.amount) * 100)
      : 0;

    // Calculate percentage based on total budget
    const totalBudget = this.budgetDataService.totalBudget();
    const monthlyPercentage = totalBudget > 0 ? Math.round((expensesMonthData.averageMonthlyAmount / totalBudget) * 100) : 12.5;

    const result = {
      userId: userEmail,
      monthlySpending: {
        amount: expensesMonthData.averageMonthlyAmount,
        percentage: monthlyPercentage,
        percentageChange,
        comparedPeriod: 'last month',
        monthlyBreakdown
      },
      trendData,
      currency: 'TND'
    };

    console.log('Spending section result:', result);
    return result;
  }

  /**
   * Build stats grid data
   */
  private buildStatsGrid(userEmail: string): StatsGridData {
    const spendingData = this.buildSpendingSection(userEmail);
    const categoryData = this.buildCategoryExpenses(userEmail);
    
    const topCategory = categoryData.categories.length > 0 
      ? categoryData.categories[0] 
      : { name: 'No Data', amount: 0, percentage: 0, transactionCount: 0 };

    const totalTransactions = categoryData.categories.reduce((sum, cat) => sum + cat.transactionCount, 0);
    const averageTransaction = totalTransactions > 0 ? categoryData.totalAmount / totalTransactions : 0;

    return {
      userId: userEmail,
      monthlyPercentage: spendingData.monthlySpending.percentage,
      topCategory: {
        name: topCategory.name,
        amount: topCategory.amount,
        percentage: topCategory.percentage,
        transactionCount: topCategory.transactionCount
      },
      totalTransactions,
      averageTransaction,
      currency: 'TND'
    };
  }

  /**
   * Build category expenses data
   */
  private buildCategoryExpenses(userEmail: string): CategoryExpensesData {
    // This would typically come from transaction data
    // For now, we'll use mock data that matches the existing structure
    const mockCategories: CategoryExpenseItem[] = [
      { name: 'Groceries', amount: 450, percentage: 35, transactionCount: 12, color: '#6B48FF', trend: 'up' },
      { name: 'Electronics', amount: 300, percentage: 25, transactionCount: 8, color: '#A855F7', trend: 'down' },
      { name: 'Clothing', amount: 240, percentage: 20, transactionCount: 6, color: '#48BB78', trend: 'stable' },
      { name: 'Entertainment', amount: 150, percentage: 12, transactionCount: 4, color: '#F56565', trend: 'up' },
      { name: 'Other', amount: 100, percentage: 8, transactionCount: 3, color: '#ED8936', trend: 'stable' }
    ];

    const totalAmount = mockCategories.reduce((sum, cat) => sum + cat.amount, 0);

    return {
      userId: userEmail,
      categories: mockCategories,
      totalAmount,
      currency: 'TND'
    };
  }

  /**
   * Build product expenses data
   */
  private buildProductExpenses(userEmail: string): ProductExpensesData {
    // This would typically come from transaction data
    // For now, we'll use mock data that matches the existing structure
    const mockProducts: ProductExpenseItem[] = [
      { name: 'Laptop', amount: 250, percentage: 30, transactionCount: 1, category: 'Electronics', trend: 'up' },
      { name: 'Smartphone', amount: 200, percentage: 25, transactionCount: 1, category: 'Electronics', trend: 'stable' },
      { name: 'Headphones', amount: 100, percentage: 15, transactionCount: 2, category: 'Electronics', trend: 'down' },
      { name: 'Groceries Weekly', amount: 80, percentage: 10, transactionCount: 4, category: 'Food', trend: 'stable' },
      { name: 'Coffee', amount: 50, percentage: 6, transactionCount: 10, category: 'Food', trend: 'up' }
    ];

    const totalAmount = mockProducts.reduce((sum, prod) => sum + prod.amount, 0);

    return {
      userId: userEmail,
      products: mockProducts,
      totalAmount,
      currency: 'TND'
    };
  }

  /**
   * Get color for index (cycling through predefined colors)
   */
  private getColorForIndex(index: number): string {
    const colors = ['#6B48FF', '#A855F7', '#48BB78', '#F56565', '#ED8936', '#3182CE', '#805AD5'];
    return colors[index % colors.length];
  }

  /**
   * Clear analytics data (for logout)
   */
  clearAnalyticsData(): void {
    this._analyticsState.set({
      analytics: null,
      savingsGoalProgress: null,
      budgetSpentProgress: null,
      spendingSection: null,
      statsGrid: null,
      categoryExpenses: null,
      productExpenses: null,
      isLoading: false,
      lastSync: null,
      error: null
    });
    console.log('Shopper analytics data cleared');
  }

  /**
   * Refresh analytics data (signals handle automatic updates, but this can force a refresh)
   */
  refreshAnalyticsData(): Observable<boolean> {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      return of(false);
    }

    console.log('Manual refresh requested - signals should handle automatic updates');
    // Force refresh of underlying services to trigger signal updates
    this.cardAuthDataService.refreshTransactions();

    // The budget and financial savings services will automatically reload when auth changes
    // or when data is modified, so we don't need to manually refresh them

    return of(true);
  }

  /**
   * Add test data for analytics (for development/testing)
   */
  addTestAnalyticsData(): void {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot add test analytics data: No authenticated user');
      return;
    }

    console.log('Adding test analytics data for shopper analytics...');

    // Add test budget data
    this.addTestBudgetData();

    // Add test financial savings data
    this.addTestFinancialSavingsData();

    // Add test transaction data for card auth service
    this.addTestTransactionData();

    console.log('Test analytics data added successfully - signals will auto-update the UI');
  }

  /**
   * Add test budget data
   */
  private addTestBudgetData(): void {
    // Add test budgets
    const testBudgets = [
      {
        name: 'Food & Groceries',
        amount: 800,
        category: 'Food',
        period: 'OneMonth' as any,
        notifications: { budgetOverrun: true, riskOfOverrun: true }
      },
      {
        name: 'Transportation',
        amount: 300,
        category: 'Transportation',
        period: 'OneMonth' as any,
        notifications: { budgetOverrun: true, riskOfOverrun: true }
      },
      {
        name: 'Entertainment',
        amount: 200,
        category: 'Entertainment',
        period: 'OneMonth' as any,
        notifications: { budgetOverrun: true, riskOfOverrun: true }
      }
    ];

    testBudgets.forEach(budget => {
      this.budgetDataService.addBudget(budget).subscribe(newBudget => {
        if (newBudget) {
          // Add some spending to the budget
          const spentAmount = Math.floor(budget.amount * (0.3 + Math.random() * 0.4)); // 30-70% spent
          this.budgetDataService.addTicketsToBudget(newBudget.id, spentAmount).subscribe();
          console.log(`Added test budget: ${budget.name} with ${spentAmount} TND spent`);
        }
      });
    });
  }

  /**
   * Add test financial savings data
   */
  private addTestFinancialSavingsData(): void {
    const testSavingsPlans = [
      {
        name: 'Emergency Fund',
        objective: 5000,
        current: 3200,
        period: 'OneYear' as any,
        frequency: 'Monthly' as any,
        startDate: new Date('2024-01-01'),
        interestRate: 2.5
      },
      {
        name: 'Vacation Fund',
        objective: 2000,
        current: 800,
        period: 'SixMonths' as any,
        frequency: 'Monthly' as any,
        startDate: new Date('2024-06-01'),
        interestRate: 1.5
      },
      {
        name: 'New Car',
        objective: 15000,
        current: 4500,
        period: 'Custom' as any,
        frequency: 'Monthly' as any,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2026-01-01'),
        interestRate: 1.8
      }
    ];

    testSavingsPlans.forEach(plan => {
      this.financialSavingsDataService.addSavingPlan(plan).subscribe(newPlan => {
        if (newPlan) {
          console.log(`Added test saving plan: ${plan.name}`);
        }
      });
    });
  }

  /**
   * Add test transaction data for card auth service
   */
  private addTestTransactionData(): void {
    const currentUser = this.authService.currentUser();
    if (!currentUser) return;

    // Create test transactions
    const testTransactions = [
      { date: '2024-01-15', time: '14:30', amount: '45.50' },
      { date: '2024-01-20', time: '09:15', amount: '120.00' },
      { date: '2024-02-05', time: '16:45', amount: '75.25' },
      { date: '2024-02-12', time: '11:20', amount: '200.00' },
      { date: '2024-02-28', time: '13:10', amount: '89.75' },
      { date: '2024-03-08', time: '10:30', amount: '156.50' },
      { date: '2024-03-15', time: '15:45', amount: '67.25' },
      { date: '2024-03-22', time: '12:00', amount: '234.00' },
      { date: '2024-04-03', time: '14:15', amount: '98.50' },
      { date: '2024-04-10', time: '09:30', amount: '145.75' },
      { date: '2024-04-18', time: '16:20', amount: '78.25' },
      { date: '2024-04-25', time: '11:45', amount: '189.00' },
      { date: '2024-05-02', time: '13:30', amount: '112.50' },
      { date: '2024-05-09', time: '10:15', amount: '267.25' },
      { date: '2024-05-16', time: '15:00', amount: '89.75' },
      { date: '2024-05-23', time: '12:45', amount: '156.00' },
      { date: '2024-05-30', time: '14:30', amount: '203.50' }
    ];

    // Store transactions in localStorage
    const userTransactionKey = `receeto-transactions_${currentUser.email}`;
    localStorage.setItem(userTransactionKey, JSON.stringify(testTransactions));

    console.log(`Added ${testTransactions.length} test transactions for user: ${currentUser.email}`);

    // Refresh the card auth service to load the new data
    this.cardAuthDataService.refreshTransactions();
  }

  /**
   * Clear test data (for development/testing)
   */
  clearTestAnalyticsData(): void {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot clear test analytics data: No authenticated user');
      return;
    }

    console.log('Clearing test analytics data...');

    // Clear budget data
    this.budgetDataService.clearBudgetData();

    // Clear financial savings data (we need to clear individual plans)
    const plans = this.financialSavingsDataService.plans();
    plans.forEach(plan => {
      this.financialSavingsDataService.deleteSavingPlan(plan.id).subscribe();
    });

    // Clear transaction data
    const userTransactionKey = `receeto-transactions_${currentUser.email}`;
    localStorage.removeItem(userTransactionKey);
    this.cardAuthDataService.refreshTransactions();

    // Clear analytics data
    this.clearAnalyticsData();

    console.log('Test analytics data cleared successfully');
  }
}
