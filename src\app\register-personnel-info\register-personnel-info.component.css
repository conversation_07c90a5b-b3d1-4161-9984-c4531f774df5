/* Reset default styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  .container {
    display: flex;
    height: 100vh;
    font-family: Arial, sans-serif;
  }

  .left-section {
    flex: 1;
    padding: 40px;
    background: #f5f5f5 !important;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    align-items: center;
    color: #333333 !important;
  }

  .right-section {
    flex: 1;
    background: linear-gradient(135deg, #6b48ff, #a855f7);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 40px;
    color: white;
    height: 100%;
    min-height: 100vh;
    position: relative;
    border-bottom-left-radius: 50px;
  }

  .back-link {
    position: absolute;
    top: 20px;
    left: 20px;
    color: #666 !important;
    text-decoration: none;
    font-size: 0.9rem;
    z-index: 1;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 20px;
    transition: background-color 0.3s;
  }

  .back-link:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  h1 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333333 !important;
  }

  p {
    margin-bottom: 20px;
    color: #666 !important;
    font-size: 0.9rem;
  }

  form {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
    max-width: 400px;
  }

  input, select {
    padding: 12px;
    border: 1px solid #ccc !important;
    border-radius: 20px;
    font-size: 1rem;
    width: 100%;
    box-sizing: border-box;
    background-color: #ffffff !important;
    color: #333333 !important;
  }

  .gender {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    color: #666 !important;
  }

  .gender label {
    margin-right: 10px;
  }

  .gender input[type="radio"] {
    width: auto;
    margin-right: 5px;
  }

  .submit-btn {
    padding: 12px;
    background: #6b48ff;
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    text-transform: uppercase;
    transition: background 0.3s;
    width: 100%;
  }

  .submit-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
  }

  footer {
    margin-top: 20px;
    color: #666 !important;
    font-size: 0.8rem;
  }

  .right-section h2 {
    font-size: 3.5rem;
    font-weight: bold;
    letter-spacing: 2px;
    margin: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
  }

  .right-section nav {
    display: flex;
    gap: 20px;
    justify-content: flex-start;
    margin-top: auto;
  }

  .right-section nav a {
    color: white;
    text-decoration: none;
    font-size: 0.9rem;
    padding: 5px 10px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    transition: background 0.3s;
  }

  .right-section nav a:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  /* Dark mode overrides - ensure register page stays in light mode */
  :host {
    /* Force light mode styles regardless of theme */
    color: #333 !important;
  }

  /* Fix for Angular form classes in dark mode */
  .ng-untouched, .ng-pristine, .ng-invalid, .ng-valid {
    background-color: #ffffff !important;
    color: #333333 !important;
  }

  /* Ensure select options remain visible */
  select option {
    background-color: #ffffff !important;
    color: #333333 !important;
  }

  /* Ensure radio buttons remain visible */
  input[type="radio"] {
    background-color: #ffffff !important;
    border-color: #666 !important;
  }

  /* Ensure date input remains visible */
  input[type="date"] {
    background-color: #ffffff !important;
    color: #333333 !important;
  }