<div class="container">
  <div class="form-section">
    <a class="back-link" (click)="goBack()">← Back to website</a>
    <h1>Forgot Password</h1>
    <p>Enter your email to receive a password reset link.</p>
    <form (ngSubmit)="onSubmit()" #forgotPasswordForm="ngForm">
      <input type="email" placeholder="Email" name="email" [(ngModel)]="model.email" required email>
      <button type="submit" class="create-account-btn" [disabled]="!forgotPasswordForm.valid">SEND RESET LINK</button>
    </form>
    <a class="back-to-login-btn" routerLink="/login">Back to Login</a>
    <p class="error-message" *ngIf="errorMessage">{{ errorMessage }}</p>
    <footer>© 2023 Receeto ALL RIGHTS RESERVED.</footer>
  </div>
  <div class="right-section">
    <div class="receeto-logo"></div>
    <div class="bottom-nav">
      <a href="https://receeto.com" target="_blank" class="nav-link">Website</a>
      <a href="#" class="nav-link">Documentation</a>
      <a href="#" class="nav-link">Terms of Use</a>
      <a href="#" class="nav-link">Blog</a>
    </div>
  </div>
</div>