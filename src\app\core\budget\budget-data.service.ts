import { Injectable, signal, computed, effect, inject } from '@angular/core';
import { Observable, of } from 'rxjs';
import { BudgetData, BudgetCategoryData, BudgetState, BudgetPeriod, BudgetNotifications } from '../../interfaces/budget';
import { AuthService } from '../auth/auth.service';

@Injectable({
  providedIn: 'root'
})
export class BudgetDataService {
  private authService = inject(AuthService);

  // Private signals for budget data
  private _budgetState = signal<BudgetState>({
    budgets: [],
    categories: [],
    totalBudget: 0,
    totalSpent: 0,
    totalRemaining: 0,
    percentSpent: 0,
    percentRemaining: 0,
    isLoading: false
  });

  private _currentUserEmail = signal<string | null>(null);

  // Public readonly signals
  readonly budgetState = this._budgetState.asReadonly();
  readonly isLoading = computed(() => this._budgetState().isLoading);

  // Computed signals for easy access to specific data
  readonly budgets = computed(() => this._budgetState().budgets);
  readonly categories = computed(() => this._budgetState().categories);
  readonly totalBudget = computed(() => this._budgetState().totalBudget);
  readonly totalSpent = computed(() => this._budgetState().totalSpent);
  readonly totalRemaining = computed(() => this._budgetState().totalRemaining);
  readonly percentSpent = computed(() => this._budgetState().percentSpent);
  readonly percentRemaining = computed(() => this._budgetState().percentRemaining);

  constructor() {
    // Watch for auth user changes and load corresponding budget data using effect
    effect(() => {
      const authUser = this.authService.currentUser();
      console.log('BudgetDataService effect triggered - authUser:', authUser);
      if (authUser) {
        this._currentUserEmail.set(authUser.email);
        this.loadUserBudgetData(authUser.email);
      } else {
        this._currentUserEmail.set(null);
        this.clearBudgetData();
      }
    }, { allowSignalWrites: true });

    // Effect to save budget data when it changes
    effect(() => {
      const budgetState = this._budgetState();
      const userEmail = this._currentUserEmail();
      if (userEmail && (budgetState.budgets.length > 0 || budgetState.categories.length > 0)) {
        this.saveBudgetDataToStorage(budgetState, userEmail);
      }
    }, { allowSignalWrites: true });
  }

  /**
   * Load budget data for a specific user email
   */
  private loadUserBudgetData(email: string): void {
    console.log('Loading budget data for email:', email);
    this._budgetState.update(state => ({ ...state, isLoading: true }));

    if (!email) {
      console.error('Cannot load budget data: No email provided');
      this._budgetState.update(state => ({ ...state, isLoading: false }));
      return;
    }

    try {
      const budgetData = this.getBudgetDataFromStorage(email);
      if (budgetData) {
        console.log(`Found budget data in storage for ${email}`);
        this._budgetState.set({ ...budgetData, isLoading: false });
      } else {
        console.log(`No budget data found for ${email}, initializing empty state`);
        this.initializeEmptyBudgetState();
      }
    } catch (error) {
      console.error('Error loading budget data:', error);
      this.initializeEmptyBudgetState();
    }
  }

  /**
   * Initialize empty budget state
   */
  private initializeEmptyBudgetState(): void {
    this._budgetState.set({
      budgets: [],
      categories: [],
      totalBudget: 0,
      totalSpent: 0,
      totalRemaining: 0,
      percentSpent: 0,
      percentRemaining: 0,
      isLoading: false
    });
  }

  /**
   * Get budget data from localStorage
   */
  private getBudgetDataFromStorage(email: string): BudgetState | null {
    try {
      if (!email) {
        console.error('Cannot get budget data from storage: No email provided');
        return null;
      }

      const budgetStorageKey = `budgetData_${email}`;
      const categoryStorageKey = `budgetCategories_${email}`;
      
      const storedBudgets = localStorage.getItem(budgetStorageKey);
      const storedCategories = localStorage.getItem(categoryStorageKey);
      
      if (storedBudgets || storedCategories) {
        const budgets: BudgetData[] = storedBudgets ? JSON.parse(storedBudgets) : [];
        const categories: BudgetCategoryData[] = storedCategories ? JSON.parse(storedCategories) : [];
        
        // Convert date strings back to Date objects
        const processedBudgets = budgets.map(budget => ({
          ...budget,
          createdAt: new Date(budget.createdAt)
        }));

        // Calculate totals
        const totals = this.calculateTotals(processedBudgets);

        return {
          budgets: processedBudgets,
          categories,
          ...totals,
          isLoading: false
        };
      }

      return null;
    } catch (error) {
      console.error('Error getting budget data from storage:', error);
      return null;
    }
  }

  /**
   * Save budget data to localStorage
   */
  private saveBudgetDataToStorage(budgetState: BudgetState, email: string): void {
    try {
      if (!email) {
        console.error('Cannot save budget data to storage: No email provided');
        return;
      }

      const budgetStorageKey = `budgetData_${email}`;
      const categoryStorageKey = `budgetCategories_${email}`;
      
      localStorage.setItem(budgetStorageKey, JSON.stringify(budgetState.budgets));
      localStorage.setItem(categoryStorageKey, JSON.stringify(budgetState.categories));
      
      console.log('Budget data saved for:', email);
    } catch (error) {
      console.error('Error saving budget data to storage:', error);
    }
  }

  /**
   * Calculate totals from budget array
   */
  private calculateTotals(budgets: BudgetData[]): {
    totalBudget: number;
    totalSpent: number;
    totalRemaining: number;
    percentSpent: number;
    percentRemaining: number;
  } {
    if (budgets.length === 0) {
      return {
        totalBudget: 0,
        totalSpent: 0,
        totalRemaining: 0,
        percentSpent: 0,
        percentRemaining: 0
      };
    }

    const totalBudget = budgets.reduce((sum, budget) => sum + budget.amount, 0);
    const totalSpent = budgets.reduce((sum, budget) => sum + (budget.spent || 0), 0);
    const totalRemaining = totalBudget - totalSpent;
    const percentSpent = totalBudget > 0 ? Math.round((totalSpent / totalBudget) * 100) : 0;
    const percentRemaining = 100 - percentSpent;

    return {
      totalBudget,
      totalSpent,
      totalRemaining,
      percentSpent,
      percentRemaining
    };
  }

  /**
   * Clear budget data (for logout)
   */
  clearBudgetData(): void {
    // Only clear the in-memory state, don't remove localStorage data
    // This ensures we don't lose data when switching between accounts
    this.initializeEmptyBudgetState();
    console.log('Budget data cleared from memory only');
  }

  /**
   * Get available budget categories
   */
  getAvailableCategories(): string[] {
    return ['Food', 'Transportation', 'Housing', 'Entertainment', 'Clothing', 'Healthcare', 'Education', 'Utilities', 'Other'];
  }

  /**
   * Add a new budget
   */
  addBudget(budget: Omit<BudgetData, 'id' | 'createdAt' | 'userId' | 'spent' | 'remaining' | 'percentSpent'>): Observable<BudgetData> {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot add budget: No authenticated user');
      return of(null as any);
    }

    const newBudget: BudgetData = {
      ...budget,
      id: Date.now().toString(),
      createdAt: new Date(),
      userId: currentUser.email,
      spent: 0,
      remaining: budget.amount,
      percentSpent: 0
    };

    const currentState = this._budgetState();
    const updatedBudgets = [...currentState.budgets, newBudget];

    // Update categories
    const updatedCategories = this.addBudgetToCategories(newBudget, currentState.categories);

    // Calculate new totals
    const totals = this.calculateTotals(updatedBudgets);

    // Update state
    this._budgetState.set({
      ...currentState,
      budgets: updatedBudgets,
      categories: updatedCategories,
      ...totals
    });

    console.log(`Added budget for user ${currentUser.email}:`, newBudget);
    return of(newBudget);
  }

  /**
   * Update an existing budget
   */
  updateBudget(updatedBudget: BudgetData): Observable<BudgetData> {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot update budget: No authenticated user');
      return of(null as any);
    }

    const currentState = this._budgetState();
    const budgetIndex = currentState.budgets.findIndex(b => b.id === updatedBudget.id && b.userId === currentUser.email);

    if (budgetIndex === -1) {
      console.error('Budget not found or user not authorized');
      return of(null as any);
    }

    const oldBudget = currentState.budgets[budgetIndex];
    const updatedBudgets = [...currentState.budgets];
    updatedBudgets[budgetIndex] = { ...updatedBudget, userId: currentUser.email };

    // Update categories if needed
    let updatedCategories = currentState.categories;
    if (oldBudget.category !== updatedBudget.category || oldBudget.amount !== updatedBudget.amount) {
      updatedCategories = this.updateBudgetInCategories(oldBudget, updatedBudget, currentState.categories);
    }

    // Calculate new totals
    const totals = this.calculateTotals(updatedBudgets);

    // Update state
    this._budgetState.set({
      ...currentState,
      budgets: updatedBudgets,
      categories: updatedCategories,
      ...totals
    });

    console.log(`Updated budget for user ${currentUser.email}:`, updatedBudget);
    return of(updatedBudget);
  }

  /**
   * Delete a budget
   */
  deleteBudget(budgetId: string): Observable<boolean> {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot delete budget: No authenticated user');
      return of(false);
    }

    const currentState = this._budgetState();
    const budgetToDelete = currentState.budgets.find(b => b.id === budgetId && b.userId === currentUser.email);

    if (!budgetToDelete) {
      console.error('Budget not found or user not authorized');
      return of(false);
    }

    const updatedBudgets = currentState.budgets.filter(b => b.id !== budgetId);
    const updatedCategories = this.removeBudgetFromCategories(budgetToDelete, currentState.categories);

    // Calculate new totals
    const totals = this.calculateTotals(updatedBudgets);

    // Update state
    this._budgetState.set({
      ...currentState,
      budgets: updatedBudgets,
      categories: updatedCategories,
      ...totals
    });

    console.log(`Deleted budget for user ${currentUser.email}:`, budgetId);
    return of(true);
  }

  /**
   * Get budget by ID
   */
  getBudgetById(budgetId: string): BudgetData | undefined {
    const currentUser = this.authService.currentUser();
    if (!currentUser) return undefined;

    return this._budgetState().budgets.find(b => b.id === budgetId && b.userId === currentUser.email);
  }

  /**
   * Add ticket expenses to a budget
   */
  addTicketsToBudget(budgetId: string, amount: number): Observable<BudgetData | null> {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot add tickets to budget: No authenticated user');
      return of(null);
    }

    const currentState = this._budgetState();
    const budgetIndex = currentState.budgets.findIndex(b => b.id === budgetId && b.userId === currentUser.email);

    if (budgetIndex === -1) {
      console.error('Budget not found or user not authorized');
      return of(null);
    }

    const budget = currentState.budgets[budgetIndex];
    const updatedBudget: BudgetData = {
      ...budget,
      spent: (budget.spent || 0) + amount,
      remaining: budget.amount - ((budget.spent || 0) + amount),
      percentSpent: Math.round(((budget.spent || 0) + amount) / budget.amount * 100)
    };

    const updatedBudgets = [...currentState.budgets];
    updatedBudgets[budgetIndex] = updatedBudget;

    // Update category spending
    const updatedCategories = this.updateCategorySpending(budget.category, amount, currentState.categories);

    // Calculate new totals
    const totals = this.calculateTotals(updatedBudgets);

    // Update state
    this._budgetState.set({
      ...currentState,
      budgets: updatedBudgets,
      categories: updatedCategories,
      ...totals
    });

    console.log(`Added tickets to budget for user ${currentUser.email}:`, updatedBudget);
    return of(updatedBudget);
  }

  /**
   * Add budget to categories
   */
  private addBudgetToCategories(budget: BudgetData, currentCategories: BudgetCategoryData[]): BudgetCategoryData[] {
    if (!budget.category || !budget.amount) return currentCategories;

    const categories = [...currentCategories];
    const categoryName = budget.category + ' Budget';
    const categoryIndex = categories.findIndex(c => c.name === categoryName && c.userId === budget.userId);

    if (categoryIndex !== -1) {
      // Update existing category
      categories[categoryIndex] = {
        ...categories[categoryIndex],
        amount: categories[categoryIndex].amount + budget.amount,
        remaining: (categories[categoryIndex].remaining || 0) + budget.amount,
        spent: categories[categoryIndex].spent || 0
      };
    } else {
      // Create new category
      categories.push({
        name: categoryName,
        amount: budget.amount,
        percentOfTotal: 0, // Will be calculated below
        spent: 0,
        remaining: budget.amount,
        percentSpent: 0,
        userId: budget.userId
      });
    }

    // Recalculate percentages for this user's categories
    return this.recalculateCategoryPercentages(categories, budget.userId);
  }

  /**
   * Update budget in categories
   */
  private updateBudgetInCategories(oldBudget: BudgetData, newBudget: BudgetData, currentCategories: BudgetCategoryData[]): BudgetCategoryData[] {
    let categories = [...currentCategories];

    // If category changed, handle both old and new categories
    if (oldBudget.category !== newBudget.category) {
      // Remove from old category
      categories = this.removeBudgetFromCategories(oldBudget, categories);
      // Add to new category
      categories = this.addBudgetToCategories(newBudget, categories);
    } else if (oldBudget.amount !== newBudget.amount) {
      // Only amount changed, update existing category
      const categoryName = newBudget.category + ' Budget';
      const categoryIndex = categories.findIndex(c => c.name === categoryName && c.userId === newBudget.userId);

      if (categoryIndex !== -1) {
        const category = categories[categoryIndex];
        categories[categoryIndex] = {
          ...category,
          amount: category.amount - oldBudget.amount + newBudget.amount,
          remaining: (category.remaining || 0) - oldBudget.amount + newBudget.amount
        };

        // Recalculate percentages
        categories = this.recalculateCategoryPercentages(categories, newBudget.userId);
      }
    }

    return categories;
  }

  /**
   * Remove budget from categories
   */
  private removeBudgetFromCategories(budget: BudgetData, currentCategories: BudgetCategoryData[]): BudgetCategoryData[] {
    const categories = [...currentCategories];
    const categoryName = budget.category + ' Budget';
    const categoryIndex = categories.findIndex(c => c.name === categoryName && c.userId === budget.userId);

    if (categoryIndex !== -1) {
      const category = categories[categoryIndex];
      const newAmount = category.amount - budget.amount;

      if (newAmount <= 0) {
        // Remove category if amount becomes 0 or negative
        categories.splice(categoryIndex, 1);
      } else {
        // Update category
        categories[categoryIndex] = {
          ...category,
          amount: newAmount,
          remaining: (category.remaining || 0) - budget.amount,
          percentSpent: newAmount > 0 ? Math.round(((category.spent || 0) / newAmount) * 100) : 0
        };
      }

      // Recalculate percentages
      return this.recalculateCategoryPercentages(categories, budget.userId);
    }

    return categories;
  }

  /**
   * Update category spending
   */
  private updateCategorySpending(categoryName: string, amount: number, currentCategories: BudgetCategoryData[]): BudgetCategoryData[] {
    if (!categoryName) return currentCategories;

    const currentUser = this.authService.currentUser();
    if (!currentUser) return currentCategories;

    const categories = [...currentCategories];
    const fullCategoryName = categoryName + ' Budget';
    const categoryIndex = categories.findIndex(c => c.name === fullCategoryName && c.userId === currentUser.email);

    if (categoryIndex !== -1) {
      const category = categories[categoryIndex];
      const newSpent = (category.spent || 0) + amount;

      categories[categoryIndex] = {
        ...category,
        spent: newSpent,
        remaining: category.amount - newSpent,
        percentSpent: category.amount > 0 ? Math.round((newSpent / category.amount) * 100) : 0
      };
    }

    return categories;
  }

  /**
   * Recalculate category percentages for a specific user
   */
  private recalculateCategoryPercentages(categories: BudgetCategoryData[], userId: string): BudgetCategoryData[] {
    // Filter categories for this user
    const userCategories = categories.filter(c => c.userId === userId);
    const otherCategories = categories.filter(c => c.userId !== userId);

    // Calculate total amount for this user
    const totalAmount = userCategories.reduce((sum, category) => sum + category.amount, 0);

    // Update percentages for user's categories
    const updatedUserCategories = userCategories.map(category => ({
      ...category,
      percentOfTotal: totalAmount > 0 ? Math.round((category.amount / totalAmount) * 100) : 0,
      percentSpent: category.amount > 0 ? Math.round(((category.spent || 0) / category.amount) * 100) : 0
    }));

    // Return combined categories
    return [...updatedUserCategories, ...otherCategories];
  }
}
