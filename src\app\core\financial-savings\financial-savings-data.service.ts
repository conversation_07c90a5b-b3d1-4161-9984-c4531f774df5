import { Injectable, signal, computed, effect, inject } from '@angular/core';
import { Observable, of } from 'rxjs';
import { SavingPlanData, SavingPlanState, SavingPeriod, SavingFrequency } from '../../interfaces/financial-savings';
import { AuthService } from '../auth/auth.service';

@Injectable({
  providedIn: 'root'
})
export class FinancialSavingsDataService {
  private authService = inject(AuthService);

  // Private signals for financial savings data
  private _savingPlanState = signal<SavingPlanState>({
    plans: [],
    totalPlans: 0,
    totalObjective: 0,
    totalCurrent: 0,
    totalRemaining: 0,
    overallProgress: 0,
    isLoading: false
  });

  private _currentUserEmail = signal<string | null>(null);

  // Public readonly signals
  public readonly savingPlanState = this._savingPlanState.asReadonly();
  public readonly currentUserEmail = this._currentUserEmail.asReadonly();

  // Computed signals for derived state
  public readonly plans = computed(() => this._savingPlanState().plans);
  public readonly totalPlans = computed(() => this._savingPlanState().totalPlans);
  public readonly totalObjective = computed(() => this._savingPlanState().totalObjective);
  public readonly totalCurrent = computed(() => this._savingPlanState().totalCurrent);
  public readonly totalRemaining = computed(() => this._savingPlanState().totalRemaining);
  public readonly overallProgress = computed(() => this._savingPlanState().overallProgress);
  public readonly isLoading = computed(() => this._savingPlanState().isLoading);

  constructor() {
    // Watch for auth user changes and load corresponding saving plan data using effect
    effect(() => {
      const authUser = this.authService.currentUser();
      console.log('FinancialSavingsDataService effect triggered - authUser:', authUser);
      if (authUser) {
        this._currentUserEmail.set(authUser.email);
        this.loadUserSavingPlanData(authUser.email);
      } else {
        this._currentUserEmail.set(null);
        this.clearSavingPlanData();
      }
    }, { allowSignalWrites: true });

    // Effect to save saving plan data when it changes
    effect(() => {
      const savingPlanState = this._savingPlanState();
      const userEmail = this._currentUserEmail();
      if (userEmail && savingPlanState.plans.length > 0) {
        this.saveSavingPlanDataToStorage(savingPlanState, userEmail);
      }
    }, { allowSignalWrites: true });
  }

  /**
   * Load user-specific saving plan data from storage
   */
  private loadUserSavingPlanData(userEmail: string): void {
    this._savingPlanState.update(state => ({ ...state, isLoading: true }));

    try {
      const storageKey = `saving_plans_${userEmail}`;
      const storedData = localStorage.getItem(storageKey);
      
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        const plans: SavingPlanData[] = parsedData.plans?.map((plan: any) => ({
          ...plan,
          startDate: new Date(plan.startDate),
          endDate: plan.endDate ? new Date(plan.endDate) : undefined,
          lastUpdate: new Date(plan.lastUpdate)
        })) || [];

        this.updateSavingPlanState(plans);
        console.log(`Loaded ${plans.length} saving plans for user: ${userEmail}`);
      } else {
        this.clearSavingPlanData();
      }
    } catch (error) {
      console.error('Error loading saving plan data:', error);
      this.clearSavingPlanData();
    }
  }

  /**
   * Update saving plan state with calculations
   */
  private updateSavingPlanState(plans: SavingPlanData[]): void {
    const totalObjective = plans.reduce((sum, plan) => sum + plan.objective, 0);
    const totalCurrent = plans.reduce((sum, plan) => sum + plan.current, 0);
    const totalRemaining = totalObjective - totalCurrent;
    const overallProgress = totalObjective > 0 ? (totalCurrent / totalObjective) * 100 : 0;

    this._savingPlanState.set({
      plans,
      totalPlans: plans.length,
      totalObjective,
      totalCurrent,
      totalRemaining,
      overallProgress,
      isLoading: false
    });
  }

  /**
   * Save saving plan data to storage for the current user
   */
  private saveSavingPlanDataToStorage(savingPlanState: SavingPlanState, userEmail: string): void {
    try {
      const storageKey = `saving_plans_${userEmail}`;
      const dataToSave = {
        plans: savingPlanState.plans,
        lastUpdated: new Date().toISOString()
      };
      
      localStorage.setItem(storageKey, JSON.stringify(dataToSave));
      console.log(`Saved saving plan data for user: ${userEmail}`);
    } catch (error) {
      console.error('Error saving saving plan data:', error);
    }
  }

  /**
   * Clear all saving plan data
   */
  private clearSavingPlanData(): void {
    this._savingPlanState.set({
      plans: [],
      totalPlans: 0,
      totalObjective: 0,
      totalCurrent: 0,
      totalRemaining: 0,
      overallProgress: 0,
      isLoading: false
    });
  }

  /**
   * Get available saving periods
   */
  getAvailablePeriods(): SavingPeriod[] {
    return Object.values(SavingPeriod);
  }

  /**
   * Get available saving frequencies
   */
  getAvailableFrequencies(): SavingFrequency[] {
    return Object.values(SavingFrequency);
  }

  /**
   * Add a new saving plan
   */
  addSavingPlan(plan: Omit<SavingPlanData, 'id' | 'lastUpdate' | 'userId' | 'percentComplete'>): Observable<SavingPlanData> {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot add saving plan: No authenticated user');
      return of(null as any);
    }

    const newPlan: SavingPlanData = {
      ...plan,
      id: Date.now().toString(),
      lastUpdate: new Date(),
      userId: currentUser.email,
      percentComplete: plan.objective > 0 ? (plan.current / plan.objective) * 100 : 0
    };

    this._savingPlanState.update(state => {
      const updatedPlans = [...state.plans, newPlan];
      this.updateSavingPlanStateFromPlans(updatedPlans);
      return this._savingPlanState();
    });

    console.log('Added new saving plan:', newPlan);
    return of(newPlan);
  }

  /**
   * Update an existing saving plan
   */
  updateSavingPlan(updatedPlan: SavingPlanData): Observable<SavingPlanData> {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot update saving plan: No authenticated user');
      return of(null as any);
    }

    // Ensure the plan belongs to the current user
    if (updatedPlan.userId !== currentUser.email) {
      console.error('Cannot update saving plan: Plan does not belong to current user');
      return of(null as any);
    }

    // Update calculations
    updatedPlan.lastUpdate = new Date();
    updatedPlan.percentComplete = updatedPlan.objective > 0 ? (updatedPlan.current / updatedPlan.objective) * 100 : 0;

    this._savingPlanState.update(state => {
      const planIndex = state.plans.findIndex(plan => plan.id === updatedPlan.id);
      if (planIndex === -1) {
        console.error('Plan not found for update');
        return state;
      }

      const updatedPlans = [...state.plans];
      updatedPlans[planIndex] = updatedPlan;
      this.updateSavingPlanStateFromPlans(updatedPlans);
      return this._savingPlanState();
    });

    console.log('Updated saving plan:', updatedPlan);
    return of(updatedPlan);
  }

  /**
   * Delete a saving plan
   */
  deleteSavingPlan(planId: string): Observable<boolean> {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot delete saving plan: No authenticated user');
      return of(false);
    }

    this._savingPlanState.update(state => {
      const planToDelete = state.plans.find(plan => plan.id === planId);
      
      // Ensure the plan belongs to the current user
      if (planToDelete && planToDelete.userId !== currentUser.email) {
        console.error('Cannot delete saving plan: Plan does not belong to current user');
        return state;
      }

      const updatedPlans = state.plans.filter(plan => plan.id !== planId);
      this.updateSavingPlanStateFromPlans(updatedPlans);
      return this._savingPlanState();
    });

    console.log('Deleted saving plan with ID:', planId);
    return of(true);
  }

  /**
   * Add funds to a saving plan
   */
  addFundsToSavingPlan(planId: string, amount: number): Observable<SavingPlanData | null> {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot add funds: No authenticated user');
      return of(null);
    }

    let updatedPlan: SavingPlanData | null = null;

    this._savingPlanState.update(state => {
      const planIndex = state.plans.findIndex(plan => plan.id === planId);
      if (planIndex === -1) {
        console.error('Plan not found for adding funds');
        return state;
      }

      const plan = state.plans[planIndex];
      
      // Ensure the plan belongs to the current user
      if (plan.userId !== currentUser.email) {
        console.error('Cannot add funds: Plan does not belong to current user');
        return state;
      }

      updatedPlan = {
        ...plan,
        current: plan.current + amount,
        lastUpdate: new Date()
      };
      updatedPlan.percentComplete = updatedPlan.objective > 0 ? (updatedPlan.current / updatedPlan.objective) * 100 : 0;

      const updatedPlans = [...state.plans];
      updatedPlans[planIndex] = updatedPlan;
      this.updateSavingPlanStateFromPlans(updatedPlans);
      return this._savingPlanState();
    });

    console.log(`Added ${amount} to saving plan ${planId}`);
    return of(updatedPlan);
  }

  /**
   * Get a specific saving plan by ID
   */
  getSavingPlanById(planId: string): SavingPlanData | undefined {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      return undefined;
    }

    const plan = this._savingPlanState().plans.find(plan => plan.id === planId);
    
    // Ensure the plan belongs to the current user
    if (plan && plan.userId !== currentUser.email) {
      return undefined;
    }

    return plan;
  }

  /**
   * Helper method to update state from plans array
   */
  private updateSavingPlanStateFromPlans(plans: SavingPlanData[]): void {
    const totalObjective = plans.reduce((sum, plan) => sum + plan.objective, 0);
    const totalCurrent = plans.reduce((sum, plan) => sum + plan.current, 0);
    const totalRemaining = totalObjective - totalCurrent;
    const overallProgress = totalObjective > 0 ? (totalCurrent / totalObjective) * 100 : 0;

    this._savingPlanState.update(state => ({
      ...state,
      plans,
      totalPlans: plans.length,
      totalObjective,
      totalCurrent,
      totalRemaining,
      overallProgress
    }));
  }

  /**
   * Add fake/test data for testing dashboard display
   */
  addTestSavingPlans(): void {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot add test data: No authenticated user');
      return;
    }

    const testPlans = [
      {
        name: 'Vacation Fund',
        objective: 2000,
        current: 1200,
        period: SavingPeriod.OneYear,
        frequency: SavingFrequency.Monthly,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        interestRate: 2.5,
        durationAmount: 200,
        durationValue: 10,
        durationType: 'months'
      },
      {
        name: 'Emergency Fund',
        objective: 5000,
        current: 1800,
        period: SavingPeriod.OneYear,
        frequency: SavingFrequency.Monthly,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2025-01-01'),
        interestRate: 3.0,
        durationAmount: 500,
        durationValue: 10,
        durationType: 'months'
      },
      {
        name: 'New Car',
        objective: 15000,
        current: 4500,
        period: SavingPeriod.Custom,
        frequency: SavingFrequency.Monthly,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2026-01-01'),
        interestRate: 1.8,
        durationAmount: 750,
        durationValue: 20,
        durationType: 'months'
      }
    ];

    // Add each test plan
    testPlans.forEach(planData => {
      this.addSavingPlan(planData).subscribe(newPlan => {
        if (newPlan) {
          console.log(`Added test saving plan: ${newPlan.name}`);
        }
      });
    });

    console.log(`Added ${testPlans.length} test saving plans for dashboard testing`);
  }

  /**
   * Clear all saving plans for the current user (useful for testing)
   */
  clearAllSavingPlans(): void {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot clear plans: No authenticated user');
      return;
    }

    this.clearSavingPlanData();

    // Also clear from localStorage
    const storageKey = `saving_plans_${currentUser.email}`;
    localStorage.removeItem(storageKey);

    console.log('Cleared all saving plans for current user');
  }
}
