import { Component, OnInit, HostListener, inject, computed } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { HttpClientModule } from '@angular/common/http';
import { TransactionService } from '../services/transaction.service';
import { AppStorageService } from '../services/app-storage.service';
import { AuthWarningComponent } from '../shared/auth-warning/auth-warning.component';
import { AuthService } from '../core/auth/auth.service';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { SwiperOptions } from 'swiper/types';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

// Define product interface
interface Product {
  name: string;
  brand: string;
  image: string;
  additionalImages: string[];
}

interface Receipt {
  merchantName: string;
  merchantLogo: string;
  date: string;
  receiptNumber: string;
  productId?: string;
  items: Array<{
    description: string;
    quantity: number;
    price: number;
    vat: number;
    total: number;
  }>;
  products?: Array<{
    name: string;
    price: string;
  }>;
  totalAmount: number;
  paymentMethod: string;
  discounts?: number;
  rating?: number;
  status?: string;
  brandName?: string;
  brandLogo?: string;
}

@Component({
  selector: 'app-shopper-tickets-history',
  standalone: true,
  imports: [CommonModule, FormsModule, TopNavbarComponent, SidebarComponent, HttpClientModule, AuthWarningComponent],
  templateUrl: './shopper-tickets-history.component.html',
  styleUrls: ['./shopper-tickets-history.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ShopperTicketsHistoryComponent implements OnInit {
  // Inject auth service
  private authService = inject(AuthService);

  // Auth signals
  isAuthenticated = computed(() => this.authService.isAuthenticated());
  currentUser = computed(() => this.authService.currentUser());

  receipt: Receipt = {
    merchantName: 'STRASS SOUSSE MALL',
    merchantLogo: 'assets/images/Strass Sahloul logo.png',
    date: '07/12/2023',
    receiptNumber: '00123',
    items: [
      {
        description: 'Blouse White',
        quantity: 1,
        price: 79.90,
        vat: 19,
        total: 95.081
      },
      {
        description: 'Blouse White',
        quantity: 1,
        price: 79.90,
        vat: 19,
        total: 95.081
      }
    ],
    totalAmount: 90.081,
    paymentMethod: 'Cash',
    discounts: -5.081,
    rating: 4.7
  };

  // All products organized by brand
  allProducts: { [key: string]: Product[] } = {
    // Zen brand products
    zen: [
      {
        name: 'Black T-shirt',
        brand: 'zen',
        image: 'assets/images/T-Shirt 1 (3).jpg',
        additionalImages: []
      },
      {
        name: 'White T-shirt',
        brand: 'zen',
        image: 'assets/images/T-Shirt 1 (1).jpg',
        additionalImages: []
      },
      {
        name: 'Striped T-shirt',
        brand: 'zen',
        image: 'assets/images/T-Shirt 1 (2).jpg',
        additionalImages: []
      }
    ],
    // Aziza brand products
    aziza: [
      {
        name: 'Couscous Package',
        brand: 'aziza',
        image: 'assets/images/aziza 1.jpg',
        additionalImages: []
      },
      {
        name: 'Prepared Couscous',
        brand: 'aziza',
        image: 'assets/images/aziza 2.jpg',
        additionalImages: []
      },
      {
        name: 'Denim Jeans',
        brand: 'aziza',
        image: 'assets/images/aziza 3.jpg',
        additionalImages: []
      }
    ],
    // Monoprix brand products
    monoprix: [
      {
        name: 'Round Hat',
        brand: 'monoprix',
        image: 'assets/images/monoprix 1.jpg',
        additionalImages: []
      },
      {
        name: 'Side View Hat',
        brand: 'monoprix',
        image: 'assets/images/monoprix 2.jpg',
        additionalImages: []
      },
      {
        name: 'Back View Hat',
        brand: 'monoprix',
        image: 'assets/images/monoprix 3.jpg',
        additionalImages: []
      }
    ],
    // Carrefour brand products (placeholder images)
    carrefour: [
      {
        name: 'Carrefour Product 1',
        brand: 'carrefour',
        image: 'assets/images/carrefour 1.webp',
        additionalImages: []
      },
      {
        name: 'Carrefour Product 2',
        brand: 'carrefour',
        image: 'assets/images/carrefour 2.webp',
        additionalImages: []
      },
      {
        name: 'Carrefour Product 3',
        brand: 'carrefour',
        image: 'assets/images/carrefour 3.webp',
        additionalImages: []
      }
    ]
  };

  // Current brand to display products for
  currentBrand: string = 'zen'; // Default to 'zen' brand

  // Current brand products to display (will be filtered based on current brand)
  recommendedProducts: any[] = [];

  // Rating variables
  userRating: number = 0;
  ratingSubmitted: boolean = false;
  // Store brand ratings to track which brands the user has rated and their ratings
  brandRatings: Map<string, number> = new Map<string, number>();

  // Newsletter subscription
  emailAddress: string = '';
  subscriptionSubmitted: boolean = false;
  subscriptionHidden: boolean = false;
  isPreviouslyUsedEmail: boolean = false;
  // Store subscribed brands to track which brands the user has subscribed to
  subscribedBrands: Set<string> = new Set<string>();

  isMobile = window.innerWidth <= 768;
  swiperConfig: SwiperOptions = {
    slidesPerView: this.isMobile ? 1 : 3,
    spaceBetween: 20,
    navigation: true,
    pagination: { clickable: true },
    breakpoints: {
      320: {
        slidesPerView: 1,
        spaceBetween: 10
      },
      768: {
        slidesPerView: 3,
        spaceBetween: 20
      }
    }
  };

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.isMobile = window.innerWidth <= 768;
    this.swiperConfig.slidesPerView = this.isMobile ? 1 : 3;
  }

  constructor(
    private route: ActivatedRoute,
    private transactionService: TransactionService,
    private appStorage: AppStorageService
  ) {}

  ngOnInit() {
    // Subscribe to the transaction service to get transaction data
    this.transactionService.currentTransaction.subscribe(transaction => {
      if (transaction) {
        // If we have transaction data from the service, use it
        this.updateReceiptFromTransaction(transaction);
        // Check subscription and rating status after receipt data is loaded
        this.checkPreviousSubscription();
        this.checkPreviousRatings();

        // Set current brand based on transaction data
        if (transaction.brandName) {
          const brandLower = transaction.brandName.toLowerCase();
          // Check if this is one of our supported brands
          if (this.isCollaboratingBrand(brandLower)) {
            this.currentBrand = brandLower;
            console.log(`Setting current brand to: ${this.currentBrand}`);
          } else {
            // Default to zen if brand not found
            this.currentBrand = 'zen';
            console.log(`Brand ${brandLower} not found, defaulting to zen`);
          }
        }

        // Filter products by current brand
        this.filterProductsByBrand();
      } else {
        // Otherwise, get the ticket ID from the route and fetch data
        this.route.params.subscribe(params => {
          const ticketId = params['id'];
          // In a real app, we would fetch the receipt data using the ticketId from a service
          // For now we're simulating fetching transaction data
          this.getTransactionDetails(ticketId);
        });
      }
    });

    // Initialize with default brand products
    this.filterProductsByBrand();
  }

  // Filter products to show only those from the current brand
  filterProductsByBrand() {
    if (this.allProducts[this.currentBrand]) {
      this.recommendedProducts = this.allProducts[this.currentBrand];
    } else {
      // Default to zen if brand not found
      this.recommendedProducts = this.allProducts['zen'];
      this.currentBrand = 'zen';
    }
  }

  getTransactionDetails(ticketId: string) {
    // In a real app, this would be a service call to get transaction details by ID
    // For demonstration, we're creating mock data that matches the transaction format

    // Simulating API call delay
    setTimeout(() => {
      // Mock transaction data based on ticket ID
      const transaction = {
        ticketNumber: ticketId,
        productId: 'P-' + ticketId,
        productName: 'Blouse White',
        productImage: 'assets/images/blouse.jpg',
        date: '07/12/2023',
        amount: '79.90 TND',
        paymentMode: 'Credit Card',
        status: 'Completed',
        quantity: 1,
        rating: 4.5,
        brandName: 'Carrefour',
        brandLogo: 'assets/images/brands/carrefour.png'
      };

      this.updateReceiptFromTransaction(transaction);
      // Check subscription and rating status after receipt data is loaded
      this.checkPreviousSubscription();
      this.checkPreviousRatings();

      // Set current brand based on transaction data
      if (transaction.brandName) {
        const brandLower = transaction.brandName.toLowerCase();
        // Check if this is one of our supported brands
        if (this.isCollaboratingBrand(brandLower)) {
          this.currentBrand = brandLower;
          console.log(`Setting current brand to: ${this.currentBrand}`);
        } else {
          // Default to zen if brand not found
          this.currentBrand = 'zen';
          console.log(`Brand ${brandLower} not found, defaulting to zen`);
        }
      }

      // Filter products by current brand
      this.filterProductsByBrand();
    }, 300);
  }

  updateReceiptFromTransaction(transaction: any) {
    // Extract the numerical amount from the transaction
    let amount: number;

    if (typeof transaction.amount === 'string') {
      // Remove currency symbol and convert to number
      amount = parseFloat(transaction.amount.replace(/[^\d.-]/g, ''));
    } else {
      amount = transaction.amount;
    }

    // Calculate VAT (19%)
    const vatRate = 19;
    const priceWithoutVat = amount / (1 + vatRate/100);

    // Create receipt items array
    let receiptItems = [];
    let productsList = [];

    // Check if transaction has products array (for registered brands)
    if (transaction.products && transaction.products.length > 0) {
      // Log the products array for debugging
      console.log('Transaction products:', transaction.products);

      // Use the products array from the transaction, ensuring each product has proper price formatting
      productsList = transaction.products.map((product: any) => {
        // Make sure price has TND suffix if it doesn't already
        const price = typeof product.price === 'string'
          ? (product.price.includes('TND') ? product.price : `${product.price} TND`)
          : `${product.price} TND`;

        return {
          name: product.name,
          price: price
        };
      });

      // Log the processed products list
      console.log('Processed products list:', productsList);

      // Create receipt items from each product
      receiptItems = transaction.products.map((product: any) => {
        // Extract price as number from product price string
        let productPrice = typeof product.price === 'string'
          ? parseFloat(product.price.replace(/[^\d.-]/g, ''))
          : product.price;

        return {
          description: product.name,
          quantity: 1,
          price: productPrice / (1 + vatRate/100), // Calculate price without VAT
          vat: vatRate,
          total: productPrice
        };
      });
    } else {
      // Create a single receipt item from transaction
      receiptItems = [{
        description: transaction.productName,
        quantity: transaction.quantity || 1,
        price: priceWithoutVat,
        vat: vatRate,
        total: amount
      }];

      // Create a single product entry
      productsList = [{
        name: transaction.productName,
        price: typeof transaction.amount === 'string' ? transaction.amount : `${transaction.amount} TND`
      }];
    }

    // Update the receipt object
    this.receipt = {
      merchantName: transaction.brandName || 'STRASS SOUSSE MALL',
      merchantLogo: transaction.brandLogo || 'assets/images/Strass Sahloul logo.png',
      date: transaction.date,
      receiptNumber: transaction.ticketNumber?.toString() || '',
      productId: transaction.productId,
      items: receiptItems,
      products: productsList,
      totalAmount: amount,
      paymentMethod: transaction.paymentMode || 'Cash',
      rating: transaction.rating || 4.7,
      status: transaction.status || 'Completed',
      brandName: transaction.brandName,
      brandLogo: transaction.brandLogo
    };

    console.log('Updated receipt with products:', this.receipt.products);
  }

  // Rating methods
  setRating(rating: number): void {
    this.userRating = rating;

    // Update star UI in the template
    const stars = document.querySelectorAll('.rating-stars i');
    stars.forEach((star, index) => {
      if (index < rating) {
        star.className = 'fas fa-star';
      } else {
        star.className = 'far fa-star';
      }
    });
  }

  submitRating(): void {
    if (this.userRating > 0) {
      // In a real app, this would send the rating to a backend service
      console.log(`Submitted rating: ${this.userRating} stars for ${this.receipt.merchantName}`);

      // Store the rating for this specific brand
      this.brandRatings.set(this.receipt.merchantName, this.userRating);

      // Save to storage service
      this.appStorage.saveBrandRatings(Array.from(this.brandRatings.entries()));

      // Update UI to show the submitted state
      this.ratingSubmitted = true;
    } else {
      alert('Please select a rating before submitting.');
    }
  }

  // Check if user has previously rated the current brand
  checkPreviousRatings(): void {
    // Reset rating state when checking
    this.ratingSubmitted = false;
    this.userRating = 0;

    // Get saved ratings from storage service
    const savedRatings = this.appStorage.getBrandRatings();

    if (savedRatings && savedRatings.length > 0) {
      // Restore the saved ratings
      this.brandRatings = new Map<string, number>(savedRatings);

      // Check if the current brand has been rated
      if (this.receipt.merchantName && this.brandRatings.has(this.receipt.merchantName)) {
        // If they've already rated this brand, set the rating and mark as submitted
        this.userRating = this.brandRatings.get(this.receipt.merchantName) || 0;
        this.ratingSubmitted = true;
      }
    }
  }

  // Newsletter subscription
  subscribeToNewsletter(): void {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (this.emailAddress && emailRegex.test(this.emailAddress)) {
      // In a real app, this would send the email to a backend service
      console.log(`Subscribed to newsletter with email: ${this.emailAddress} for brand: ${this.receipt.merchantName}`);

      // Add this brand to the set of subscribed brands
      this.subscribedBrands.add(this.receipt.merchantName);

      // Store the email and subscribed brands using the storage service
      this.appStorage.saveSubscribedEmail(this.emailAddress);
      this.appStorage.saveSubscribedBrands(Array.from(this.subscribedBrands));

      // Show success message
      this.subscriptionSubmitted = true;

      // After 2 seconds, hide the entire newsletter section
      setTimeout(() => {
        this.subscriptionHidden = true;
      }, 2000);
    } else {
      alert('Please enter a valid email address.');
    }
  }

  // Check if user has previously subscribed to the current brand
  checkPreviousSubscription(): void {
    // Reset subscription state when checking
    this.subscriptionHidden = false;
    this.subscriptionSubmitted = false;

    // Get saved data from storage service
    const savedEmail = this.appStorage.getSubscribedEmail();
    const savedBrands = this.appStorage.getSubscribedBrands();

    if (savedEmail) {
      // Restore the email and mark it as previously used
      this.emailAddress = savedEmail;
      this.isPreviouslyUsedEmail = true;

      if (savedBrands && savedBrands.length > 0) {
        // Restore the set of subscribed brands
        this.subscribedBrands = new Set<string>(savedBrands);

        // Check if the current brand is in the set of subscribed brands
        if (this.receipt.merchantName && this.subscribedBrands.has(this.receipt.merchantName)) {
          // If they've already subscribed to this specific brand, hide the newsletter section
          this.subscriptionHidden = true;
        }
      }
    } else {
      this.isPreviouslyUsedEmail = false;
    }
  }

  // Method to handle product recommendation clicks
  viewRecommendedProduct(product: any): void {
    // In a real app, this would navigate to the product page
    console.log(`Viewing product: ${product.name} from ${product.brand}`);
    alert(`You clicked on ${product.name} from ${product.brand.toUpperCase()} brand.`);
  }

  refundArticle() {
    // TODO: Implement refund logic
    alert('Refund functionality coming soon!');
  }

  exportToPDF() {
    const receiptCard = document.querySelector('.receipt-card') as HTMLElement;
    html2canvas(receiptCard, {
      scale: 2,
      ignoreElements: (element) => element.classList?.contains('no-export')
    }).then(canvas => {
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF({ orientation: 'p', unit: 'mm', format: 'a4' });
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const imgProps = pdf.getImageProperties(imgData);
      let pdfWidth = pageWidth;
      let pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
      if (pdfHeight > pageHeight) {
        pdfHeight = pageHeight;
        pdfWidth = (imgProps.width * pdfHeight) / imgProps.height;
      }
      const x = (pageWidth - pdfWidth) / 2;
      const y = 10;
      pdf.addImage(imgData, 'PNG', x, y, pdfWidth, pdfHeight);
      pdf.save('receipt.pdf');
    });
  }

  isCollaboratingBrand(brandName: string): boolean {
    const brandLower = brandName.toLowerCase();
    return brandLower in this.allProducts;
  }
}
