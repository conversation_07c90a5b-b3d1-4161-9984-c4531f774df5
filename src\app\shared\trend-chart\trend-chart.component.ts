import { Component, AfterViewInit, Input, ElementRef, ViewChild, OnDestroy, OnChanges, SimpleChanges, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Chart, LineController, LineElement, PointElement, LinearScale, CategoryScale } from 'chart.js';

// Register only the components we need for better performance
Chart.register(LineController, LineElement, PointElement, LinearScale, CategoryScale);

@Component({
  selector: 'app-trend-chart',
  standalone: true,
  imports: [CommonModule],
  template: '<canvas #canvas></canvas>',
  styles: [`
    :host {
      display: block;
      width: 100%;
      height: 100%;
    }
    canvas {
      max-width: 100%;
      height: auto;
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TrendChartComponent implements AfterViewInit, OnDestroy, OnChanges {
  @ViewChild('canvas') canvas!: ElementRef;
  @Input() data: number[] = [];
  private chart?: Chart;
  private resizeObserver?: ResizeObserver;

  ngAfterViewInit() {
    if (this.canvas) {
      this.createChart();
      this.setupResizeObserver();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['data'] && this.chart && !changes['data'].firstChange) {
      this.updateChart();
    }
  }

  ngOnDestroy() {
    if (this.chart) {
      this.chart.destroy();
      this.chart = undefined;
    }
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = undefined;
    }
  }

  private createChart() {
    const ctx = this.canvas.nativeElement.getContext('2d');

    this.chart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: Array(this.data.length).fill(''),
        datasets: [{
          data: this.data,
          borderColor: '#6B48FF',
          borderWidth: 2,
          tension: 0.4,
          fill: false,
          pointRadius: 0,
          pointHoverRadius: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        animation: false, // Disable animations for better performance
        interaction: {
          intersect: false,
          mode: 'index'
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            enabled: false // Disable tooltips for better performance
          }
        },
        scales: {
          x: {
            display: false,
            grid: {
              display: false
            }
          },
          y: {
            display: false,
            grid: {
              display: false
            }
          }
        },
        elements: {
          line: {
            borderJoinStyle: 'round'
          }
        }
      }
    });
  }

  private updateChart() {
    if (this.chart) {
      this.chart.data.labels = Array(this.data.length).fill('');
      this.chart.data.datasets[0].data = this.data;
      this.chart.update('none'); // Use 'none' mode for instant updates
    }
  }

  private setupResizeObserver() {
    if (typeof ResizeObserver !== 'undefined') {
      this.resizeObserver = new ResizeObserver(() => {
        if (this.chart) {
          this.chart.resize();
        }
      });
      this.resizeObserver.observe(this.canvas.nativeElement);
    }
  }
}