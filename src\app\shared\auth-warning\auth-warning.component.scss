.auth-warning {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50vh;
  padding: 20px;

  .warning-card {
    background: #fff;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    width: 100%;

    i {
      font-size: 3rem;
      color: #ffc107;
      margin-bottom: 20px;
    }

    h3 {
      color: #333;
      margin-bottom: 15px;
      font-size: 1.5rem;
    }

    p {
      color: #666;
      margin-bottom: 25px;
      line-height: 1.5;
    }

    .login-btn {
      background: #A78BFA;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 1rem;
      cursor: pointer;
      text-decoration: none;
      display: inline-block;
      transition: background-color 0.3s ease;

      &:hover {
        background: #9333EA;
      }
    }
  }
}

// Dark mode support
:host-context(.dark-mode) {
  .auth-warning {
    .warning-card {
      background: #2d3748;
      color: #e2e8f0;

      h3 {
        color: #e2e8f0;
      }

      p {
        color: #a0aec0;
      }

      i {
        color: #f6e05e;
      }
    }
  }
}
