.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: var(--card-bg, white);
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  border: 1px solid rgba(171, 85, 247, 0.2);
  box-shadow:
    0 10px 30px rgba(107, 72, 255, 0.15),
    0 6px 15px rgba(168, 85, 247, 0.1),
    0 0 0 1px rgba(171, 85, 247, 0.05),
    inset 0 0 0 1px rgba(255, 255, 255, 0.3);
  animation: modalGlow 1.5s ease-in-out infinite alternate;

  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(107, 72, 255, 0.3) transparent;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    margin: 10px 0;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(107, 72, 255, 0.3);
    border-radius: 10px;
    border: 2px solid transparent;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(107, 72, 255, 0.5);
  }

  /* Hide scrollbar when not needed */
  &.no-scroll {
    overflow: hidden;
  }

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, rgba(107, 72, 255, 0.4), rgba(168, 85, 247, 0.4), rgba(183, 148, 244, 0.4));
    border-radius: 14px;
    z-index: -1;
    opacity: 0.5;
    filter: blur(8px);
  }
}

@keyframes modalGlow {
  from {
    box-shadow:
      0 10px 30px rgba(107, 72, 255, 0.15),
      0 6px 15px rgba(168, 85, 247, 0.1),
      0 0 0 1px rgba(171, 85, 247, 0.05),
      inset 0 0 0 1px rgba(255, 255, 255, 0.3);
  }
  to {
    box-shadow:
      0 15px 40px rgba(107, 72, 255, 0.2),
      0 8px 20px rgba(168, 85, 247, 0.15),
      0 0 0 1px rgba(171, 85, 247, 0.1),
      inset 0 0 0 1px rgba(255, 255, 255, 0.4);
  }
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color, #eee);
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--text-color, #333);
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-color, #666);
    padding: 5px;

    &:hover {
      color: var(--text-color, #333);
      opacity: 0.8;
    }
  }
}

.input-mode-selector {
  padding: 0 20px;
  border-bottom: 1px solid rgba(171, 85, 247, 0.2);
  background: linear-gradient(to bottom, rgba(107, 72, 255, 0.03), var(--card-bg, rgba(255, 255, 255, 0)));

  .mode-tabs {
    display: flex;
    gap: 15px;
    margin-bottom: -1px;
    padding-top: 15px;

    .mode-tab {
      padding: 14px 24px;
      background: var(--secondary-bg, rgba(245, 245, 245, 0.8));
      border: 1px solid rgba(171, 85, 247, 0.15);
      border-bottom: none;
      border-radius: 12px 12px 0 0;
      cursor: pointer;
      font-weight: 600;
      color: var(--text-color, #666);
      display: flex;
      align-items: center;
      gap: 10px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      box-shadow: 0 -4px 10px rgba(107, 72, 255, 0.05);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #6B48FF, #A855F7);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      i {
        font-size: 18px;
        transition: transform 0.3s ease;
      }

      &:hover {
        background: rgba(250, 250, 250, 0.95);
        color: #A855F7;
        transform: translateY(-2px);

        i {
          transform: scale(1.2);
        }

        &::before {
          opacity: 0.5;
        }
      }

      &.active {
        background: var(--card-bg, white);
        color: #6B48FF;
        border-bottom: 2px solid var(--card-bg, white);
        position: relative;
        transform: translateY(-4px);
        box-shadow:
          0 -6px 15px rgba(107, 72, 255, 0.1),
          0 2px 0 var(--card-bg, rgba(255, 255, 255, 1));

        &::before {
          opacity: 1;
        }

        i {
          transform: scale(1.2);
          color: #A855F7;
        }

        &:after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 0;
          right: 0;
          height: 2px;
          background: var(--card-bg, white);
        }
      }
    }
  }
}

.modal-body {
  padding: 20px;
  position: relative;

  .mode-description {
    color: var(--text-color, #666);
    margin-bottom: 25px;
    font-size: 15px;
    text-align: center;
    line-height: 1.5;
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
    padding: 15px 20px;
    background: rgba(107, 72, 255, 0.03);
    border-radius: 10px;
    border: 1px solid rgba(171, 85, 247, 0.1);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(to bottom, #6B48FF, #A855F7);
      border-radius: 10px 0 0 10px;
    }
  }

  .qr-mode-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
  }

  .manual-mode-container {
    display: flex;
    flex-direction: column;
  }

  .qr-upload-section {
    position: relative;
    width: 100%;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .qr-upload-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      max-width: 300px;
      align-items: center;

      input[type="file"] {
        display: none;
      }

      .upload-btn {
        padding: 14px 28px;
        background: linear-gradient(135deg, #6B48FF, #A855F7);
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 25px;
        width: 100%;
        justify-content: center;
        transition: all 0.3s ease;
        box-shadow:
          0 4px 15px rgba(107, 72, 255, 0.25),
          0 2px 4px rgba(168, 85, 247, 0.2),
          inset 0 1px 1px rgba(255, 255, 255, 0.3);
        text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          transform: translateY(-2px) scale(1.02);
          box-shadow:
            0 8px 20px rgba(107, 72, 255, 0.3),
            0 4px 8px rgba(168, 85, 247, 0.25),
            inset 0 1px 1px rgba(255, 255, 255, 0.4);

          &::before {
            opacity: 1;
          }
        }

        &:active {
          transform: translateY(1px);
          box-shadow:
            0 2px 8px rgba(107, 72, 255, 0.2),
            0 1px 3px rgba(168, 85, 247, 0.15),
            inset 0 1px 1px rgba(255, 255, 255, 0.2);
        }

        i {
          font-size: 22px;
          filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
        }
      }

      .qr-preview {
        position: relative;
        width: 220px;
        margin-top: 15px;
        margin-left: auto;
        margin-right: auto;
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.02);
        }

        img {
          width: 100%;
          height: auto;
          border-radius: 12px;
          border: 2px solid rgba(171, 85, 247, 0.2);
          box-shadow:
            0 10px 25px rgba(107, 72, 255, 0.15),
            0 5px 10px rgba(168, 85, 247, 0.1);
          transition: all 0.3s ease;
          filter: drop-shadow(0 0 8px rgba(168, 85, 247, 0.2));
        }

        &::after {
          content: '';
          position: absolute;
          top: -5px;
          left: -5px;
          right: -5px;
          bottom: -5px;
          background: linear-gradient(135deg, rgba(107, 72, 255, 0.2), rgba(168, 85, 247, 0.2));
          border-radius: 16px;
          z-index: -1;
          opacity: 0.5;
          filter: blur(8px);
          transition: all 0.3s ease;
        }

        &:hover::after {
          opacity: 0.7;
          filter: blur(12px);
        }

        .remove-qr-btn {
          position: absolute;
          top: -10px;
          right: -10px;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: linear-gradient(135deg, #ff4d4f, #ff7875);
          color: white;
          border: none;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          box-shadow:
            0 4px 8px rgba(255, 77, 79, 0.3),
            0 2px 4px rgba(0, 0, 0, 0.2);
          transition: all 0.3s ease;
          z-index: 2;

          &:hover {
            background: linear-gradient(135deg, #ff7875, #ff4d4f);
            transform: scale(1.15) rotate(8deg);
            box-shadow:
              0 6px 12px rgba(255, 77, 79, 0.4),
              0 3px 6px rgba(0, 0, 0, 0.25);
          }

          i {
            font-size: 16px;
            filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.3));
          }
        }
      }
    }
  }

  .form-group {
    margin-bottom: 20px;

    label {
      display: block;
      margin-bottom: 8px;
      color: var(--text-color, #333);
      font-weight: 500;
    }

    input {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid var(--input-border, #ddd);
      border-radius: 4px;
      font-size: 14px;
      background-color: var(--input-bg, white);
      color: var(--input-text, #333);

      &:focus {
        outline: none;
        border-color: #6B48FF;
      }

      &.has-prefix {
        padding-left: 20px;
      }
    }

    // Autocomplete styles
    .autocomplete-container {
      position: relative;
      width: 100%;

      input {
        padding-right: 30px; // Make room for the dropdown icon
      }

      .dropdown-icon {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #6B48FF;
        pointer-events: none;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 14px;
          transition: transform 0.3s ease;
        }
      }

      // Rotate icon when dropdown is open
      &:focus-within .dropdown-icon i {
        transform: rotate(180deg);
      }

      .autocomplete-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        max-height: 250px;
        overflow-y: auto;
        overflow-x: hidden;
        background: var(--card-bg, white);
        border: 1px solid rgba(171, 85, 247, 0.15);
        border-top: none;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 8px 16px rgba(107, 72, 255, 0.1);
        z-index: 10;
        scrollbar-width: thin;
        scrollbar-color: rgba(107, 72, 255, 0.3) transparent;

        &::-webkit-scrollbar {
          width: 5px;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
          margin: 8px 0;
        }

        &::-webkit-scrollbar-thumb {
          background-color: rgba(107, 72, 255, 0.3);
          border-radius: 10px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background-color: rgba(107, 72, 255, 0.5);
        }

        .autocomplete-item {
          padding: 12px 16px;
          cursor: pointer;
          transition: all 0.2s ease;
          font-size: 14px;
          color: var(--text-color, #333);

          &:hover, &.active {
            background-color: var(--hover-bg, #f0f0ff);
            color: #6B48FF;
            transform: translateX(4px);
          }

          &:not(:last-child) {
            border-bottom: 1px solid var(--border-color, #f0f0f0);
          }
        }
      }
    }
  }

  .products-section {
    margin-top: 20px;

    h3 {
      margin-bottom: 15px;
      color: var(--text-color, #333);
    }

    .add-product-form {
      display: grid;
      grid-template-columns: 2fr 1fr auto;
      gap: 10px;
      margin-bottom: 20px;

      input {
        padding: 8px 12px;
        border: 1px solid var(--input-border, #ddd);
        border-radius: 4px;
        font-size: 14px;
        background-color: var(--input-bg, white);
        color: var(--input-text, #333);

        &:focus {
          outline: none;
          border-color: #6B48FF;
        }
      }

      button {
        padding: 8px 16px;
        background: #6B48FF;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;

        &:hover {
          opacity: 0.9;
        }
      }
    }

    .products-list {
      margin-bottom: 20px;
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;
      border: 1px solid rgba(171, 85, 247, 0.15);
      border-radius: 8px;
      box-shadow: inset 0 0 10px rgba(107, 72, 255, 0.03);

      /* Custom scrollbar styling */
      scrollbar-width: thin;
      scrollbar-color: rgba(107, 72, 255, 0.3) transparent;

      &::-webkit-scrollbar {
        width: 5px;
        height: 5px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
        margin: 8px 0;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(107, 72, 255, 0.3);
        border-radius: 10px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background-color: rgba(107, 72, 255, 0.5);
      }

      .product-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        border-bottom: 1px solid var(--border-color, #eee);
        background: var(--card-bg, #fff);
        transition: all 0.3s ease;

        &:hover {
          background: var(--hover-bg, #f8f9fa);
        }

        .product-info {
          flex: 1;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-right: 10px;

          .product-name {
            font-weight: 500;
            color: var(--text-color, #333);
          }

          .product-price {
            color: var(--text-color, #666);
            font-weight: 500;
          }
        }

        .product-edit-form {
          flex: 1;
          display: flex;
          gap: 10px;
          align-items: center;

          .edit-input {
            flex: 1;
            padding: 8px;
            border: 1px solid var(--input-border, #ddd);
            border-radius: 4px;
            font-size: 14px;
            background-color: var(--input-bg, white);
            color: var(--input-text, #333);

            &:focus {
              border-color: #007bff;
              outline: none;
            }
          }

          .edit-actions {
            display: flex;
            gap: 5px;

            button {
              padding: 6px 12px;
              border: none;
              border-radius: 4px;
              cursor: pointer;
              font-size: 14px;
              transition: all 0.2s ease;

              &.save-edit-btn {
                background: #28a745;
                color: white;

                &:hover {
                  background: #218838;
                }
              }

              &.cancel-edit-btn {
                background: #dc3545;
                color: white;

                &:hover {
                  background: #c82333;
                }
              }
            }
          }
        }

        .product-actions {
          display: flex;
          gap: 5px;

          button {
            padding: 6px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: transparent;

            &.edit-btn {
              color: #007bff;

              &:hover {
                background: #e9ecef;
              }
            }

            &.delete-btn {
              color: #dc3545;

              &:hover {
                background: #e9ecef;
              }
            }

            i {
              font-size: 14px;
            }
          }
        }
      }
    }

    .total-amount {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      background: var(--secondary-bg, #f8f9fa);
      border-radius: 4px;
      font-weight: 500;
      margin-top: 10px;
      color: var(--text-color, #333);
    }
  }
}

.modal-footer {
  padding: 24px;
  border-top: 1px solid rgba(171, 85, 247, 0.15);
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  background: linear-gradient(to top, rgba(107, 72, 255, 0.03), var(--card-bg, rgba(255, 255, 255, 0)));
  border-radius: 0 0 12px 12px;

  button {
    padding: 12px 28px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    letter-spacing: 0.3px;

    &.cancel-btn {
      background: var(--card-bg, rgba(255, 255, 255, 0.8));
      border: 1px solid rgba(171, 85, 247, 0.2);
      color: var(--text-color, #666);
      box-shadow:
        0 2px 6px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        background: var(--hover-bg, rgba(250, 250, 250, 0.95));
        color: #A855F7;
        transform: translateY(-2px);
        border-color: rgba(171, 85, 247, 0.4);
        box-shadow:
          0 4px 10px rgba(0, 0, 0, 0.08),
          inset 0 1px 0 rgba(255, 255, 255, 0.5);

        &::before {
          opacity: 1;
        }
      }

      &:active {
        transform: translateY(1px);
        box-shadow:
          0 1px 3px rgba(0, 0, 0, 0.05),
          inset 0 1px 0 rgba(255, 255, 255, 0.8);
      }
    }

    &.save-btn {
      background: linear-gradient(135deg, #6B48FF, #A855F7);
      color: white;
      border: none;
      box-shadow:
        0 4px 15px rgba(107, 72, 255, 0.25),
        0 2px 4px rgba(168, 85, 247, 0.2),
        inset 0 1px 1px rgba(255, 255, 255, 0.3);
      text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow:
          0 8px 20px rgba(107, 72, 255, 0.3),
          0 4px 8px rgba(168, 85, 247, 0.25),
          inset 0 1px 1px rgba(255, 255, 255, 0.4);

        &::before {
          opacity: 1;
        }
      }

      &:active:not(:disabled) {
        transform: translateY(1px);
        box-shadow:
          0 2px 8px rgba(107, 72, 255, 0.2),
          0 1px 3px rgba(168, 85, 247, 0.15),
          inset 0 1px 1px rgba(255, 255, 255, 0.2);
      }

      &:disabled {
        background: linear-gradient(135deg, #c4b8e2, #d8c2e9);
        cursor: not-allowed;
        box-shadow: none;
        text-shadow: none;
        opacity: 0.7;
      }
    }
  }
}