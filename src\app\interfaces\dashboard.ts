export interface DashboardData {
  id: string;
  userId: string; // Email of the user who owns this dashboard
  lastUpdated: Date;
  preferences: DashboardPreferences;
  widgets: DashboardWidget[];
  layout: DashboardLayout;
}

export interface DashboardPreferences {
  theme: 'light' | 'dark' | 'auto';
  currency: string;
  language: string;
  notifications: DashboardNotificationSettings;
  defaultView: DashboardView;
}

export interface DashboardNotificationSettings {
  budgetAlerts: boolean;
  savingsReminders: boolean;
  loyaltyCardExpiry: boolean;
  weeklyReports: boolean;
  monthlyReports: boolean;
}

export enum DashboardView {
  Overview = 'overview',
  Budget = 'budget',
  Savings = 'savings',
  LoyaltyCards = 'loyalty-cards',
  Analytics = 'analytics'
}

export interface DashboardWidget {
  id: string;
  type: DashboardWidgetType;
  title: string;
  position: WidgetPosition;
  size: WidgetSize;
  isVisible: boolean;
  config: WidgetConfig;
  data?: any; // Dynamic data based on widget type
}

export enum DashboardWidgetType {
  BudgetOverview = 'budget-overview',
  SavingsProgress = 'savings-progress',
  LoyaltyCards = 'loyalty-cards',
  RecentTransactions = 'recent-transactions',
  ExpenseCategories = 'expense-categories',
  SavingsGoals = 'savings-goals',
  QuickActions = 'quick-actions',
  MonthlySpending = 'monthly-spending'
}

export interface WidgetPosition {
  row: number;
  column: number;
  order: number;
}

export interface WidgetSize {
  width: number; // Grid units
  height: number; // Grid units
  minWidth?: number;
  minHeight?: number;
}

export interface WidgetConfig {
  refreshInterval?: number; // in milliseconds
  showHeader?: boolean;
  allowResize?: boolean;
  allowMove?: boolean;
  customSettings?: Record<string, any>;
}

export interface DashboardLayout {
  gridColumns: number;
  gridRows: number;
  gap: number;
  responsive: ResponsiveLayout;
}

export interface ResponsiveLayout {
  mobile: LayoutBreakpoint;
  tablet: LayoutBreakpoint;
  desktop: LayoutBreakpoint;
}

export interface LayoutBreakpoint {
  columns: number;
  maxWidth: number;
  widgetSizes: Record<string, WidgetSize>;
}

export interface DashboardState {
  dashboard: DashboardData | null;
  widgets: DashboardWidget[];
  isLoading: boolean;
  lastSync: Date | null;
  hasUnsavedChanges: boolean;
  error: string | null;
}

// State interfaces for the new card components
export interface CardExpensesState {
  lifetimeExpenses: LifetimeExpensesData | null;
  currentMonth: CurrentMonthData | null;
  expensesMonth: ExpensesMonthData | null;
  savingPlan: SavingPlanData | null;
  isLoading: boolean;
  error: string | null;
}

export interface DashboardAnalytics {
  totalBudget: number;
  totalSpent: number;
  totalSavings: number;
  loyaltyCardsCount: number;
  monthlyTrend: MonthlyTrend[];
  categoryBreakdown: CategoryBreakdown[];
  savingsProgress: SavingsProgress[];
}

export interface MonthlyTrend {
  month: string;
  income: number;
  expenses: number;
  savings: number;
}

export interface CategoryBreakdown {
  category: string;
  amount: number;
  percentage: number;
  color: string;
}

export interface SavingsProgress {
  planId: string;
  planName: string;
  target: number;
  current: number;
  percentage: number;
  daysRemaining: number;
}

// New interfaces for the four card components
export interface LifetimeExpensesData {
  userId: string; // Email of the user who owns this data
  totalAmount: number;
  transactionCount: number;
  accountCreationDate: Date;
  firstTransactionDate: Date | null;
  currency: string;
}

export interface CurrentMonthData {
  userId: string; // Email of the user who owns this data
  totalAmount: number;
  transactionCount: number;
  month: number; // 0-11 (January = 0)
  year: number;
  currency: string;
  dailyBreakdown: DailyExpense[];
}

export interface DailyExpense {
  day: number;
  amount: number;
  transactionCount: number;
}

export interface ExpensesMonthData {
  userId: string; // Email of the user who owns this data
  monthlyBreakdown: MonthlyExpense[];
  totalAmount: number;
  averageMonthlyAmount: number;
  currency: string;
}

export interface MonthlyExpense {
  month: number; // 0-11 (January = 0)
  year: number;
  amount: number;
  transactionCount: number;
  monthName: string; // e.g., "January 2024"
}

export interface SavingPlanData {
  userId: string; // Email of the user who owns this data
  plans: SavingPlanItem[];
  totalTarget: number;
  totalCurrent: number;
  totalPercentage: number;
  currency: string;
}

export interface SavingPlanItem {
  id: string;
  name: string;
  target: number;
  current: number;
  percentage: number;
  color: string;
  category: string;
  daysRemaining?: number;
  isActive: boolean;
}

// Type guards for Dashboard interfaces
export function isDashboardData(obj: any): obj is DashboardData {
  return obj && typeof obj === 'object' && 'id' in obj && 'userId' in obj && 'widgets' in obj;
}

export function isDashboardWidget(obj: any): obj is DashboardWidget {
  return obj && typeof obj === 'object' && 'id' in obj && 'type' in obj && 'position' in obj;
}

export function isDashboardState(obj: any): obj is DashboardState {
  return obj && typeof obj === 'object' && 'widgets' in obj && 'isLoading' in obj;
}

// Type guards for the new card component interfaces
export function isLifetimeExpensesData(obj: any): obj is LifetimeExpensesData {
  return obj && typeof obj === 'object' && 'userId' in obj && 'totalAmount' in obj && 'transactionCount' in obj;
}

export function isCurrentMonthData(obj: any): obj is CurrentMonthData {
  return obj && typeof obj === 'object' && 'userId' in obj && 'totalAmount' in obj && 'month' in obj && 'year' in obj;
}

export function isExpensesMonthData(obj: any): obj is ExpensesMonthData {
  return obj && typeof obj === 'object' && 'userId' in obj && 'monthlyBreakdown' in obj && 'totalAmount' in obj;
}

export function isSavingPlanData(obj: any): obj is SavingPlanData {
  return obj && typeof obj === 'object' && 'userId' in obj && 'plans' in obj && 'totalTarget' in obj;
}

export function isCardExpensesState(obj: any): obj is CardExpensesState {
  return obj && typeof obj === 'object' && 'isLoading' in obj && 'error' in obj;
}

// Default dashboard configuration
export const DEFAULT_DASHBOARD_CONFIG: Partial<DashboardData> = {
  preferences: {
    theme: 'auto',
    currency: 'TND',
    language: 'en',
    notifications: {
      budgetAlerts: true,
      savingsReminders: true,
      loyaltyCardExpiry: true,
      weeklyReports: false,
      monthlyReports: true
    },
    defaultView: DashboardView.Overview
  },
  layout: {
    gridColumns: 12,
    gridRows: 8,
    gap: 16,
    responsive: {
      mobile: {
        columns: 1,
        maxWidth: 768,
        widgetSizes: {}
      },
      tablet: {
        columns: 6,
        maxWidth: 1024,
        widgetSizes: {}
      },
      desktop: {
        columns: 12,
        maxWidth: 1920,
        widgetSizes: {}
      }
    }
  }
};

// Default widgets configuration
export const DEFAULT_DASHBOARD_WIDGETS: Omit<DashboardWidget, 'id'>[] = [
  {
    type: DashboardWidgetType.BudgetOverview,
    title: 'Budget Overview',
    position: { row: 0, column: 0, order: 0 },
    size: { width: 6, height: 2 },
    isVisible: true,
    config: { refreshInterval: 30000, showHeader: true, allowResize: true, allowMove: true }
  },
  {
    type: DashboardWidgetType.SavingsProgress,
    title: 'Savings Progress',
    position: { row: 0, column: 6, order: 1 },
    size: { width: 6, height: 2 },
    isVisible: true,
    config: { refreshInterval: 30000, showHeader: true, allowResize: true, allowMove: true }
  },
  {
    type: DashboardWidgetType.ExpenseCategories,
    title: 'Expense Categories',
    position: { row: 2, column: 0, order: 2 },
    size: { width: 8, height: 3 },
    isVisible: true,
    config: { refreshInterval: 60000, showHeader: true, allowResize: true, allowMove: true }
  },
  {
    type: DashboardWidgetType.LoyaltyCards,
    title: 'Loyalty Cards',
    position: { row: 2, column: 8, order: 3 },
    size: { width: 4, height: 3 },
    isVisible: true,
    config: { refreshInterval: 300000, showHeader: true, allowResize: true, allowMove: true }
  },
  {
    type: DashboardWidgetType.RecentTransactions,
    title: 'Recent Transactions',
    position: { row: 5, column: 0, order: 4 },
    size: { width: 12, height: 3 },
    isVisible: true,
    config: { refreshInterval: 15000, showHeader: true, allowResize: true, allowMove: true }
  }
];
