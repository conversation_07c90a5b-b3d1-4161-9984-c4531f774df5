const path = require('path');
const CompressionPlugin = require('compression-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

module.exports = {
  // Use production mode for optimizations
  mode: 'production',
  
  // Optimization configurations
  optimization: {
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true, // Remove console logs in production
            pure_funcs: ['console.log'] // Remove console.log specifically
          },
          output: {
            comments: false // Remove comments
          },
          mangle: true // Name mangling for smaller file sizes
        },
        extractComments: false, // Don't extract comments to separate file
        parallel: true // Use multi-process parallel running for improved build speed
      }),
    ],
    runtimeChunk: 'single', // Extract runtime code into a separate chunk
    splitChunks: {
      chunks: 'all',
      maxInitialRequests: Infinity,
      minSize: 20000,
      maxSize: 250000, // Limit chunk size to 250kb
      cacheGroups: {
        // Angular core libraries
        angular: {
          test: /[\\/]node_modules[\\/]@angular[\\/]/,
          name: 'angular',
          priority: 20,
          chunks: 'all'
        },
        // Chart.js library
        chartjs: {
          test: /[\\/]node_modules[\\/]chart\.js[\\/]/,
          name: 'chartjs',
          priority: 15,
          chunks: 'all'
        },
        // RxJS library
        rxjs: {
          test: /[\\/]node_modules[\\/]rxjs[\\/]/,
          name: 'rxjs',
          priority: 15,
          chunks: 'all'
        },
        // Swiper library
        swiper: {
          test: /[\\/]node_modules[\\/]swiper[\\/]/,
          name: 'swiper',
          priority: 15,
          chunks: 'all'
        },
        // Other vendor libraries
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name(module) {
            const packageName = module.context.match(/[\\/]node_modules[\\/]([^@\\/]+)/);
            if (!packageName) return 'vendor';
            return `npm.${packageName[1].replace('@', '')}`;
          },
          priority: 10,
          chunks: 'all'
        },
        // Common components
        common: {
          name: 'common',
          minChunks: 2,
          priority: 5,
          chunks: 'all',
          enforce: true
        },
        // Styles
        styles: {
          name: 'styles',
          test: /\.(css|scss)$/,
          chunks: 'all',
          enforce: true,
          priority: 1
        }
      }
    }
  },
  
  // Plugins for additional optimizations
  plugins: [
    // Gzip compression for assets
    new CompressionPlugin({
      algorithm: 'gzip',
      test: /\.(js|css|html|svg)$/,
      threshold: 10240, // Only compress assets bigger than 10kb
      minRatio: 0.8 // Only compress assets that compress well (80% or better)
    }),
    
    // Bundle analyzer for visualization (disabled by default, enable when needed)
    // new BundleAnalyzerPlugin()
  ],
  
  // Performance hints configuration
  performance: {
    hints: 'warning', // 'error' or false are other options
    maxAssetSize: 512000, // Size in bytes
    maxEntrypointSize: 512000, // Size in bytes
  },
};